name: Compliance (HIPAA) Tests (Phase 8)

on:
  schedule:
    - cron: '30 3 * * *'  # nightly at 3:30 AM UTC (30 min after integration)
  workflow_dispatch: {}
  push:
    branches: [develop, stripe]  # optional; not required checks

jobs:
  compliance:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          
      - name: Install dependencies
        run: pnpm -w install --frozen-lockfile
        
      - name: Create test results directory
        run: mkdir -p frontend/test-results
        
      - name: Run HIPAA compliance tests
        run: |
          cd frontend
          pnpm exec vitest --run src/components/compliance/__tests__/hipaa-redaction.test.ts --reporter=basic --reporter=junit --outputFile=test-results/compliance-junit.xml
        env:
          NODE_ENV: test
          VITEST_INTEGRATION: true
          # Compliance test environment
          HIPAA_AUDIT_ENABLED: true
          PHI_DETECTION_STRICT: true
          COMPLIANCE_MODE: test
          
      - name: Run medical copilot compliance tests
        run: |
          cd frontend
          pnpm exec vitest --run src/components/medical/__tests__/medical-copilot.integration.test.ts --reporter=basic --reporter=junit --outputFile=test-results/medical-compliance-junit.xml
        env:
          NODE_ENV: test
          VITEST_INTEGRATION: true
          # Medical AI compliance environment
          MEDICAL_AI_AUDIT_ENABLED: true
          PHI_DETECTION_STRICT: true
          DETERMINISTIC_LLM: true
          
      - name: Upload compliance test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: phase8-compliance-results
          path: frontend/test-results/
          retention-days: 30
          
      - name: Generate compliance summary
        if: always()
        run: |
          echo "## Phase 8 Compliance Test Summary" > compliance-summary.md
          echo "- HIPAA Redaction Tests: ${{ job.status }}" >> compliance-summary.md
          echo "- Medical Copilot Tests: ${{ job.status }}" >> compliance-summary.md
          echo "- Timestamp: $(date -u)" >> compliance-summary.md
          cat compliance-summary.md
          
      - name: Upload compliance summary
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: phase8-compliance-summary
          path: compliance-summary.md
          retention-days: 30
          
      - name: Comment on tracking issue (if available)
        if: always()
        run: |
          echo "Compliance tests completed with status: ${{ job.status }}"
          echo "Results uploaded to artifacts: phase8-compliance-results"
          # TODO: Add GitHub issue comment when tracking issue is available
