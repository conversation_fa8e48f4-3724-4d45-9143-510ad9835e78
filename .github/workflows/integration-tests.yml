name: Integration Tests (Phase 8)

on:
  schedule:
    - cron: '0 3 * * *'  # nightly at 3 AM UTC
  workflow_dispatch: {}
  push:
    branches: [develop, stripe]  # optional; not required checks

jobs:
  integration:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          
      - name: Install dependencies
        run: pnpm -w install --frozen-lockfile
        
      - name: Create test results directory
        run: mkdir -p frontend/test-results
        
      - name: Run integration tests
        run: |
          cd frontend
          pnpm run test:integration --reporter=junit --outputFile=test-results/integration-junit.xml
        env:
          NODE_ENV: test
          VITEST_INTEGRATION: true
          # Test environment variables
          NEXT_PUBLIC_SUPABASE_URL: http://localhost:54321
          NEXT_PUBLIC_SUPABASE_ANON_KEY: test-anon-key
          SUPABASE_SERVICE_ROLE_KEY: test-service-role-key
          OPENAI_API_KEY: test-openai-key
          DISABLE_EXTERNAL_APIS: true
          
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: phase8-integration-results
          path: frontend/test-results/
          retention-days: 30
          
      - name: Upload coverage reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: phase8-integration-coverage
          path: frontend/coverage/integration/
          retention-days: 30
          
      - name: Comment on tracking issue (if available)
        if: always()
        run: |
          echo "Integration tests completed with status: ${{ job.status }}"
          echo "Results uploaded to artifacts: phase8-integration-results"
          # TODO: Add GitHub issue comment when tracking issue is available
