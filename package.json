{"name": "pi_lawyer_ai", "version": "1.0.1", "description": "AiLex - Multi-Practice Legal AI Assistant supporting 7 practice areas", "packageManager": "pnpm@9.15.9", "scripts": {"prepare": "node -e \"if (process.env.NODE_ENV !== 'production' && !process.env.CI && !process.env.VERCEL) { require('child_process').exec('husky install') }\"", "test": "echo \"Error: no test specified\" && exit 1", "test:legacy": "cd frontend && pnpm vitest --run --config ../vitest.legacy.config.ts", "format:check": "prettier -c .", "format:write": "prettier -w .", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:ci": "eslint . --max-warnings=0", "lint:app:prod:strict": "eslint frontend/src/app frontend/src/lib frontend/src/components --ext .ts,.tsx --max-warnings=0 --ignore-pattern \"**/__tests__/**\" --ignore-pattern \"**/__mocks__/**\" --ignore-pattern \"frontend/src/lib/testing/**\"", "lint:app:prod": "eslint frontend/src/app frontend/src/lib frontend/src/components --ext .ts,.tsx --max-warnings=0", "typecheck": "tsc -p frontend/tsconfig.prod.json --noEmit", "typecheck:app": "tsc -p frontend/tsconfig.json --noEmit", "test:ci": "vitest --run --config vitest.ci.config.ts", "build:ci": "NEXT_TELEMETRY_DISABLED=1 next build", "dev:staging": "cp .env.staging .env && pnpm dev", "dev:production": "cp .env.production .env && pnpm dev", "build:staging": "cp .env.staging .env && pnpm build", "build:production": "cp .env.production .env && pnpm build", "env:setup": "node scripts/setup-env.js", "env:setup:staging": "node scripts/setup-env.js staging", "env:setup:production": "node scripts/setup-env.js production", "env:status": "node scripts/env-manager.js status", "env:staging": "node scripts/env-manager.js staging", "env:production": "node scripts/env-manager.js production", "test:auth": "node scripts/test-auth.js", "test:jwt": "node scripts/jwt-claims-test.js", "test:routes": "node scripts/auth-route-test.js", "test:single-user": "node scripts/single-user-auth-test.js", "test:tunnel": "node scripts/test-tunnel-connection.js", "test:mcp": "jest tests/mcp.e2e.spec.ts", "mcp:rotate-key": "npx ts-node scripts/rotate-mcp-key.ts", "mcp:list-keys": "npx ts-node scripts/rotate-mcp-key.ts list", "neo4j:setup": "node scripts/setup-neo4j.js", "verify:secrets": "node scripts/verify-secrets.js", "verify:endpoint": "node scripts/verify-endpoint-config.js", "tunnel": "bash scripts/start-copilotkit-tunnel.sh", "tunnel:advanced": "bash scripts/start-copilotkit-tunnel-advanced.sh", "copilotkit:login": "npx copilotkit@latest login", "copilotkit:whoami": "npx copilotkit@latest whoami", "copilotkit:dev": "npx copilotkit@latest dev --port 8000"}, "dependencies": {"@copilotkit/cli": "^0.0.6", "@copilotkit/react-core": "^1.8.11", "@copilotkit/react-ui": "^1.8.11", "@copilotkit/runtime": "^1.7.1", "@copilotkit/shared": "^1.8.11", "@google-cloud/secret-manager": "^5.6.0", "@google/generative-ai": "^0.24.0", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.40", "@langchain/langgraph": "^0.2.46", "@modelcontextprotocol/server-postgres": "^0.6.2", "@sentry/nextjs": "^9.42.1", "@sentry/node": "^9.42.1", "@sentry/react": "^9.42.1", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "@types/node": "^20.0.0", "axios": "^1.11.0", "commander": "^12.0.0", "google-auth-library": "^9.14.1", "langchain": "^0.3.30", "neo4j-driver": "^5.15.0", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "openai": "^4.93.0", "pg": "^8.13.3"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "eslint": "^9.34.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4", "eslint-plugin-vitest": "^0.5", "globals": "^15", "husky": "^9.1.7", "jest": "^29.7.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "5.8.2", "typescript-eslint": "^8"}, "husky": {"hooks": {"pre-commit": "pnpm run verify:secrets && pnpm run test:jwt && pnpm run test:auth"}}, "overrides": {"@types/react": "18.3.1", "@types/react-dom": "18.3.1", "axios": "^1.7.9", "cookie": "^0.7.2", "prismjs": "^1.29.0", "langchain": "^0.3.30", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.66"}, "pnpm": {"overrides": {"axios": "^1.7.9", "cookie": "^0.7.2", "prismjs": "^1.29.0", "langchain": "^0.3.30", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.66", "react": "18.3.1", "react-dom": "18.3.1", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "react@^19": "npm:react@18.3.1", "react-dom@^19": "npm:react-dom@18.3.1", "@types/react@^19": "npm:@types/react@18.3.1", "@types/react-dom@^19": "npm:@types/react-dom@18.3.1", "typescript": "5.8.2", "esbuild": ">=0.25.0", "esbuild@<=0.24.2": ">=0.25.0", "tmp@<=0.2.3": ">=0.2.4"}}}