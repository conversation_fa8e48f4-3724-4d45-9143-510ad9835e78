import * as React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import ProactiveContainer from "@/app/(authenticated)/dashboard/ProactiveContainer";
import { useSupabase } from "@/lib/supabase/provider";
import ProactiveMessage from "@/components/proactive/ProactiveMessage";

// Mock dependencies
vi.mock("@/lib/supabase/provider", () => ({
  useSupabase: vi.fn(),
}));

vi.mock("next/navigation", () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
  })),
}));

vi.mock("@/components/proactive/ProactiveMessage", () => {
  return {
    default: vi.fn(() => (
      <div data-testid="proactive-message">Mocked ProactiveMessage</div>
    )),
  };
});

// Mock fetch
global.fetch = vi.fn();

describe("ProactiveContainer Component", () => {
  const mockUser = {
    id: "test-user-id",
    email: "<EMAIL>",
    role: "attorney",
  };

  const mockInsights = [
    {
      id: "insight-1",
      message: "High Priority: Review settlement agreement",
      suggestions: ["View Case Details"],
      priority: 10,
      timestamp: "2025-04-10T10:00:00Z",
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useSupabase as any).mockReturnValue({
      supabase: {},
      getCurrentUser: vi.fn().mockResolvedValue(mockUser),
    });
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: vi.fn().mockResolvedValue({ insights: mockInsights }),
    });
  });

  it("fetches user data and insights on mount", async () => {
    render(<ProactiveContainer />);

    // Should show loading initially
    expect(screen.getByText(/Loading user.../i)).toBeInTheDocument();

    // Wait for user and insights to load
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining("/api/activity/insights"),
      );
    });

    // After loading, ProactiveMessage should be rendered with correct props
    await waitFor(() => {
      expect(ProactiveMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: mockUser.id,
          userName: expect.any(String),
          activities: expect.any(Array),
          insights: mockInsights,
          isLoading: false,
        }),
        expect.anything(),
      );
    });
  });

  it("handles API errors gracefully", async () => {
    // Mock fetch to return an error
    (global.fetch as any).mockResolvedValue({
      ok: false,
      status: 500,
      json: vi.fn().mockResolvedValue({ error: "Server error" }),
    });

    render(<ProactiveContainer />);

    // Wait for fetch to be called
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled();
    });

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/Error loading insights/i)).toBeInTheDocument();
    });
  });

  it("handles network errors gracefully", async () => {
    // Mock fetch to throw a network error
    (global.fetch as any).mockRejectedValue(new Error("Network error"));

    render(<ProactiveContainer />);

    // Wait for fetch to be called
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalled();
    });

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/Error loading insights/i)).toBeInTheDocument();
    });
  });
});
