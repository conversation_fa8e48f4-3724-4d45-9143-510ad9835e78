/**
 * Rate Limit Middleware Tests
 *
 * Tests for the rate limiting middleware functionality
 * with proper mocking of NextResponse and auth helpers.
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import { NextRequest } from "next/server";
import { 
  setupFakeTimers, 
  cleanupFakeTimers, 
  advanceTimeByMinutes,
  testSlidingWindow,
  TIME_CONSTANTS
} from "@/test/helpers/time-control";

// Mock next/server before importing anything else
vi.mock("next/server", async () => {
  const actual = await vi.importActual<any>("next/server");
  
  // Create a mock Response class with json method
  class MockResponse {
    status: number;
    headers: Headers;
    body: any;

    constructor(body?: any, init?: ResponseInit) {
      this.status = init?.status || 200;
      this.headers = new Headers(init?.headers);
      this.body = body;
    }

    json() {
      return Promise.resolve(this.body ? JSON.parse(this.body) : {});
    }

    text() {
      return Promise.resolve(this.body ? String(this.body) : "");
    }

    static json(data: any, init?: ResponseInit) {
      return new MockResponse(JSON.stringify(data), init);
    }
  }

  return {
    ...actual,
    NextResponse: {
      json: MockResponse.json,
      next: vi.fn(() => new MockResponse()),
      redirect: vi.fn(() => new MockResponse()),
      rewrite: vi.fn(() => new MockResponse()),
    },
  };
});

// Mock the rate limit service
const mockRateLimit = vi.fn();
vi.mock("@/lib/middlewares/rate-limit", () => ({
  rateLimit: mockRateLimit,
}));

// Mock auth helpers
vi.mock("@/lib/auth/jwt-utils", () => ({
  extractJwtFromRequest: vi.fn(),
  validateJwt: vi.fn(),
}));

import { rateLimitMiddleware } from "@/lib/middlewares/rate-limit-middleware";
import { extractJwtFromRequest, validateJwt } from "@/lib/auth/jwt-utils";

describe("Rate Limit Middleware", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    setupFakeTimers('2025-01-01T00:00:00Z');
  });

  afterEach(() => {
    cleanupFakeTimers();
  });

  describe("Basic rate limiting", () => {
    it("should allow requests under the rate limit", async () => {
      mockRateLimit.mockResolvedValue({
        success: true,
        current: 1,
        limit: 100,
        retryAfter: 0,
      });

      const request = new NextRequest("https://example.com/api/test", {
        method: "GET",
        headers: {
          Authorization: "Bearer valid-token",
        },
      });

      const response = await rateLimitMiddleware(request);

      expect(response).toBeUndefined(); // Should pass through
      expect(mockRateLimit).toHaveBeenCalledWith(request);
    });

    it("should block requests that exceed the rate limit", async () => {
      mockRateLimit.mockResolvedValue({
        success: false,
        current: 101,
        limit: 100,
        retryAfter: 60,
      });

      const request = new NextRequest("https://example.com/api/test", {
        method: "GET",
        headers: {
          Authorization: "Bearer valid-token",
        },
      });

      const response = await rateLimitMiddleware(request);

      expect(response).toBeDefined();
      expect(response?.status).toBe(429);
      
      const data = await response?.json();
      expect(data.error).toBe("Rate limit exceeded");
      expect(data.retryAfter).toBe(60);
    });

    it("should include proper headers in rate limit response", async () => {
      mockRateLimit.mockResolvedValue({
        success: false,
        current: 101,
        limit: 100,
        retryAfter: 60,
      });

      const request = new NextRequest("https://example.com/api/test", {
        method: "GET",
      });

      const response = await rateLimitMiddleware(request);

      expect(response?.headers.get("Retry-After")).toBe("60");
      expect(response?.headers.get("X-RateLimit-Limit")).toBe("100");
      expect(response?.headers.get("X-RateLimit-Remaining")).toBe("0");
    });
  });

  describe("Authentication-based rate limiting", () => {
    it("should apply different limits for authenticated users", async () => {
      vi.mocked(extractJwtFromRequest).mockReturnValue("valid-jwt-token");
      vi.mocked(validateJwt).mockResolvedValue({
        sub: "user-123",
        email: "<EMAIL>",
        aud: "authenticated",
        role: "authenticated",
        app_metadata: { tenant_id: "tenant-123" },
        user_metadata: {},
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      });

      mockRateLimit.mockResolvedValue({
        success: true,
        current: 50,
        limit: 1000, // Higher limit for authenticated users
        retryAfter: 0,
      });

      const request = new NextRequest("https://example.com/api/test", {
        method: "GET",
        headers: {
          Authorization: "Bearer valid-jwt-token",
        },
      });

      const response = await rateLimitMiddleware(request);

      expect(response).toBeUndefined(); // Should pass through
      expect(mockRateLimit).toHaveBeenCalledWith(request);
    });

    it("should apply stricter limits for unauthenticated users", async () => {
      vi.mocked(extractJwtFromRequest).mockReturnValue(undefined);

      mockRateLimit.mockResolvedValue({
        success: false,
        current: 11,
        limit: 10, // Lower limit for unauthenticated users
        retryAfter: 300,
      });

      const request = new NextRequest("https://example.com/api/test", {
        method: "GET",
      });

      const response = await rateLimitMiddleware(request);

      expect(response?.status).toBe(429);
    });
  });

  describe("Time-based rate limiting", () => {
    it("should reset rate limits after time window", async () => {
      // First request - at limit
      mockRateLimit.mockResolvedValueOnce({
        success: false,
        current: 100,
        limit: 100,
        retryAfter: 60,
      });

      const request = new NextRequest("https://example.com/api/test", {
        method: "GET",
      });

      let response = await rateLimitMiddleware(request);
      expect(response?.status).toBe(429);

      // Advance time past the window
      advanceTimeByMinutes(2);

      // Should allow requests again
      mockRateLimit.mockResolvedValueOnce({
        success: true,
        current: 1,
        limit: 100,
        retryAfter: 0,
      });

      response = await rateLimitMiddleware(request);
      expect(response).toBeUndefined(); // Should pass through
    });

    it("should handle sliding window rate limiting", async () => {
      const windowSizeMs = TIME_CONSTANTS.MINUTE * 5; // 5 minute window
      const maxRequests = 50;

      let requestCount = 0;
      mockRateLimit.mockImplementation(async () => {
        requestCount++;
        if (requestCount <= maxRequests) {
          return {
            success: true,
            current: requestCount,
            limit: maxRequests,
            retryAfter: 0,
          };
        } else {
          return {
            success: false,
            current: requestCount,
            limit: maxRequests,
            retryAfter: Math.floor(windowSizeMs / 1000),
          };
        }
      });

      const requestFn = async () => {
        const req = new NextRequest("https://example.com/api/test", {
          method: "GET",
        });
        const resp = await rateLimitMiddleware(req);
        return resp ? { blocked: true, status: resp.status } : { blocked: false };
      };

      const results = await testSlidingWindow(requestFn, windowSizeMs, maxRequests);

      expect(results.initialRequests.every(r => !r.blocked)).toBe(true);
      expect(results.exceedsLimit.blocked).toBe(true);
      expect(results.afterWindowSlide.every(r => !r.blocked)).toBe(true);
    });
  });

  describe("Error handling", () => {
    it("should handle rate limit service errors gracefully", async () => {
      mockRateLimit.mockRejectedValue(new Error("Rate limit service unavailable"));

      const request = new NextRequest("https://example.com/api/test", {
        method: "GET",
      });

      const response = await rateLimitMiddleware(request);

      // Should fail safe and allow the request through
      expect(response).toBeUndefined();
    });

    it("should handle JWT validation errors gracefully", async () => {
      vi.mocked(extractJwtFromRequest).mockReturnValue("invalid-jwt-token");
      vi.mocked(validateJwt).mockRejectedValue(new Error("Invalid JWT"));

      mockRateLimit.mockResolvedValue({
        success: true,
        current: 1,
        limit: 10, // Unauthenticated limit
        retryAfter: 0,
      });

      const request = new NextRequest("https://example.com/api/test", {
        method: "GET",
        headers: {
          Authorization: "Bearer invalid-jwt-token",
        },
      });

      const response = await rateLimitMiddleware(request);

      // Should treat as unauthenticated and apply appropriate limits
      expect(response).toBeUndefined();
      expect(mockRateLimit).toHaveBeenCalledWith(request);
    });
  });

  describe("Request method handling", () => {
    it("should apply rate limits to all HTTP methods", async () => {
      const methods = ["GET", "POST", "PUT", "DELETE", "PATCH"];

      for (const method of methods) {
        mockRateLimit.mockResolvedValue({
          success: true,
          current: 1,
          limit: 100,
          retryAfter: 0,
        });

        const request = new NextRequest("https://example.com/api/test", {
          method,
        });

        const response = await rateLimitMiddleware(request);

        expect(response).toBeUndefined();
        expect(mockRateLimit).toHaveBeenCalledWith(request);

        vi.clearAllMocks();
      }
    });

    it("should handle OPTIONS requests appropriately", async () => {
      mockRateLimit.mockResolvedValue({
        success: true,
        current: 1,
        limit: 1000, // Higher limit for OPTIONS
        retryAfter: 0,
      });

      const request = new NextRequest("https://example.com/api/test", {
        method: "OPTIONS",
      });

      const response = await rateLimitMiddleware(request);

      expect(response).toBeUndefined();
    });
  });

  describe("IP-based rate limiting", () => {
    it("should handle requests with different IP addresses", async () => {
      const ip1 = "***********";
      const ip2 = "***********";

      // IP1 exceeds limit
      mockRateLimit.mockResolvedValueOnce({
        success: false,
        current: 101,
        limit: 100,
        retryAfter: 60,
      });

      const request1 = new NextRequest("https://example.com/api/test", {
        method: "GET",
        headers: {
          "X-Forwarded-For": ip1,
        },
      });

      let response = await rateLimitMiddleware(request1);
      expect(response?.status).toBe(429);

      // IP2 should still be allowed
      mockRateLimit.mockResolvedValueOnce({
        success: true,
        current: 1,
        limit: 100,
        retryAfter: 0,
      });

      const request2 = new NextRequest("https://example.com/api/test", {
        method: "GET",
        headers: {
          "X-Forwarded-For": ip2,
        },
      });

      response = await rateLimitMiddleware(request2);
      expect(response).toBeUndefined();
    });
  });
});
