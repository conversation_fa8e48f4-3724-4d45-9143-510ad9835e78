/**
 * Comprehensive JWT Utilities Tests
 *
 * Tests for JWT token extraction, validation, and utility functions
 * covering security scenarios, edge cases, and error handling.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { NextRequest } from "next/server";
import {
  extractJwtFromRequest,
  validateJwt,
  extractOrganizationId,
  extractUserId,
  generateCopilotAuthHeader,
  JWTValidationError,
  type JWTPayload,
} from "@/lib/auth/jwt-utils";

// Mock environment variables
const mockEnv = {
  SUPABASE_JWT_SECRET: "test-jwt-secret-key-32-characters-long",
};

describe("JWT Utilities", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Set up environment variables
    process.env.SUPABASE_JWT_SECRET = mockEnv.SUPABASE_JWT_SECRET;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("extractJwtFromRequest", () => {
    it("should extract JWT from valid Authorization header", () => {
      const request = new NextRequest("https://example.com", {
        headers: {
          Authorization:
            "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token",
        },
      });

      const token = extractJwtFromRequest(request);
      expect(token).toBe("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token");
    });

    it("should return undefined when Authorization header is missing", () => {
      const request = new NextRequest("https://example.com");

      const token = extractJwtFromRequest(request);
      expect(token).toBeUndefined();
    });

    it("should return undefined when Authorization header is malformed", () => {
      const request = new NextRequest("https://example.com", {
        headers: {
          Authorization: "InvalidFormat token",
        },
      });

      const token = extractJwtFromRequest(request);
      expect(token).toBeUndefined();
    });

    it("should return undefined when Authorization header has no token", () => {
      const request = new NextRequest("https://example.com", {
        headers: {
          Authorization: "Bearer ",
        },
      });

      const token = extractJwtFromRequest(request);
      expect(token).toBeUndefined();
    });

    it("should handle case-insensitive Bearer prefix", () => {
      const request = new NextRequest("https://example.com", {
        headers: {
          Authorization: "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token",
        },
      });

      const token = extractJwtFromRequest(request);
      expect(token).toBe("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token");
    });
  });

  describe("validateJwt", () => {
    it("should validate a properly signed JWT", async () => {
      // This would require a real JWT for full testing
      // For now, test the error cases
      const invalidToken = "invalid.jwt.token";

      await expect(validateJwt(invalidToken)).rejects.toThrow(
        JWTValidationError,
      );
    });

    it("should reject expired JWT", async () => {
      const expiredToken = "expired.jwt.token";

      await expect(validateJwt(expiredToken)).rejects.toThrow(
        JWTValidationError,
      );
    });

    it("should reject JWT with invalid signature", async () => {
      const invalidSignatureToken = "invalid.signature.token";

      await expect(validateJwt(invalidSignatureToken)).rejects.toThrow(
        JWTValidationError,
      );
    });

    it("should reject malformed JWT", async () => {
      const malformedToken = "not-a-jwt";

      await expect(validateJwt(malformedToken)).rejects.toThrow(
        JWTValidationError,
      );
    });

    it("should handle missing secret gracefully", async () => {
      delete process.env.SUPABASE_JWT_SECRET;
      const token = "any.jwt.token";

      await expect(validateJwt(token)).rejects.toThrow(JWTValidationError);

      // Restore for other tests
      process.env.SUPABASE_JWT_SECRET = mockEnv.SUPABASE_JWT_SECRET;
    });
  });

  describe("extractOrganizationId", () => {
    it("should extract organization ID from valid payload", () => {
      const payload: JWTPayload = {
        sub: "user-123",
        email: "<EMAIL>",
        aud: "authenticated",
        role: "authenticated",
        app_metadata: {
          tenant_id: "org-456",
        },
        user_metadata: {},
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      };

      const orgId = extractOrganizationId(payload);
      expect(orgId).toBe("org-456");
    });

    it("should return undefined when tenant_id is missing", () => {
      const payload: JWTPayload = {
        sub: "user-123",
        email: "<EMAIL>",
        aud: "authenticated",
        role: "authenticated",
        app_metadata: {},
        user_metadata: {},
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      };

      const orgId = extractOrganizationId(payload);
      expect(orgId).toBeUndefined();
    });

    it("should return undefined when app_metadata is missing", () => {
      const payload = {
        sub: "user-123",
        email: "<EMAIL>",
        aud: "authenticated",
        role: "authenticated",
        user_metadata: {},
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      } as JWTPayload;

      const orgId = extractOrganizationId(payload);
      expect(orgId).toBeUndefined();
    });
  });

  describe("extractUserId", () => {
    it("should extract user ID from valid payload", () => {
      const payload: JWTPayload = {
        sub: "user-123",
        email: "<EMAIL>",
        aud: "authenticated",
        role: "authenticated",
        app_metadata: {},
        user_metadata: {},
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      };

      const userId = extractUserId(payload);
      expect(userId).toBe("user-123");
    });

    it("should return undefined when sub is missing", () => {
      const payload = {
        email: "<EMAIL>",
        aud: "authenticated",
        role: "authenticated",
        app_metadata: {},
        user_metadata: {},
        iat: Date.now() / 1000,
        exp: Date.now() / 1000 + 3600,
      } as JWTPayload;

      const userId = extractUserId(payload);
      expect(userId).toBeUndefined();
    });
  });

  describe("generateCopilotAuthHeader", () => {
    it("should generate auth header with valid JWT", () => {
      const token = "valid.jwt.token";

      const header = generateCopilotAuthHeader(token);
      expect(header).toBe("Bearer valid.jwt.token");
    });

    it("should handle empty token", () => {
      const token = "";

      const header = generateCopilotAuthHeader(token);
      expect(header).toBe("Bearer ");
    });
  });

  describe("JWTValidationError", () => {
    it("should create error with message", () => {
      const error = new JWTValidationError("Test error");

      expect(error.message).toBe("Test error");
      expect(error.name).toBe("JWTValidationError");
      expect(error).toBeInstanceOf(Error);
    });

    it("should create error with cause", () => {
      const cause = new Error("Original error");
      const error = new JWTValidationError("Test error", { cause });

      expect(error.message).toBe("Test error");
      expect(error.cause).toBe(cause);
    });
  });

  describe("Security edge cases", () => {
    it("should handle very long tokens", async () => {
      const longToken = "a".repeat(10000);

      await expect(validateJwt(longToken)).rejects.toThrow(JWTValidationError);
    });

    it("should handle tokens with special characters", async () => {
      const specialToken = "token.with.special!@#$%^&*()characters";

      await expect(validateJwt(specialToken)).rejects.toThrow(
        JWTValidationError,
      );
    });

    it("should handle null/undefined tokens", async () => {
      await expect(validateJwt(null as any)).rejects.toThrow(
        JWTValidationError,
      );
      await expect(validateJwt(undefined as any)).rejects.toThrow(
        JWTValidationError,
      );
    });
  });
});
