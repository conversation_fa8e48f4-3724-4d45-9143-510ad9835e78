/**
 * Super Admin Configuration Tests
 *
 * Tests for the secure super admin configuration system that replaces
 * hardcoded email lists with environment-based configuration.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import {
  getSuperAdminConfig,
  isSuperAdminEmail,
  getSuperAdminEmails,
  validateSuperAdminConfig,
  logSuperAdminConfigStatus,
} from "@/lib/auth/super-admin-config";

// Mock the environment configuration using vi.hoisted
const mockGetSafeEnvironmentConfig = vi.hoisted(() =>
  vi.fn(() => ({
    superAdminEmails: ["<EMAIL>", "<EMAIL>"],
    mcpRulesBase: "https://test.example.com",
    supabaseUrl: "https://test.supabase.co",
    supabaseAnonKey: "test-key",
    googleCloudProject: "test-project",
    mcpProject: "test-mcp",
    tenantProject: "test-tenant",
    useSupabaseAuth: true,
    featureMcpRulesEngine: true,
  })),
);

vi.mock("@/config/env", () => ({
  env: {
    superAdminEmails: ["<EMAIL>", "<EMAIL>"],
  },
  getEnvironmentConfig: vi.fn(() => mockGetSafeEnvironmentConfig()),
  getSafeEnvironmentConfig: mockGetSafeEnvironmentConfig,
}));

describe("Super Admin Configuration", () => {
  const originalEnv = process.env.NODE_ENV;

  beforeEach(() => {
    // Reset console mocks
    vi.clearAllMocks();
    vi.spyOn(console, "log").mockImplementation(() => {});
    vi.spyOn(console, "warn").mockImplementation(() => {});
    vi.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    process.env.NODE_ENV = originalEnv;
  });

  describe("getSuperAdminConfig", () => {
    it("should return environment-configured emails", () => {
      const config = getSuperAdminConfig();

      expect(config.emails).toEqual(["<EMAIL>", "<EMAIL>"]);
      expect(config.isEnvironmentConfigured).toBe(true);
      expect(config.isDatabaseDriven).toBe(false);
    });

    it("should throw error in production without environment configuration", () => {
      process.env.NODE_ENV = "production";

      // Mock empty environment configuration
      mockGetSafeEnvironmentConfig.mockReturnValueOnce({
        superAdminEmails: [],
        mcpRulesBase: "https://test.example.com",
        supabaseUrl: "https://test.supabase.co",
        supabaseAnonKey: "test-key",
        googleCloudProject: "test-project",
        mcpProject: "test-mcp",
        tenantProject: "test-tenant",
        useSupabaseAuth: true,
        featureMcpRulesEngine: true,
      });

      expect(() => getSuperAdminConfig()).toThrow("CRITICAL SECURITY ERROR");
    });
  });

  describe("isSuperAdminEmail", () => {
    it("should return true for configured super admin emails", () => {
      expect(isSuperAdminEmail("<EMAIL>")).toBe(true);
      expect(isSuperAdminEmail("<EMAIL>")).toBe(true);
    });

    it("should return false for non-configured emails", () => {
      expect(isSuperAdminEmail("<EMAIL>")).toBe(false);
      expect(isSuperAdminEmail("<EMAIL>")).toBe(false);
    });

    it("should return false for null or undefined emails", () => {
      expect(isSuperAdminEmail(null)).toBe(false);
      expect(isSuperAdminEmail(undefined)).toBe(false);
      expect(isSuperAdminEmail("")).toBe(false);
    });

    it("should handle case sensitivity correctly", () => {
      expect(isSuperAdminEmail("<EMAIL>")).toBe(true);
      expect(isSuperAdminEmail("<EMAIL>")).toBe(true);
    });
  });

  describe("getSuperAdminEmails", () => {
    it("should return array of configured emails", () => {
      const emails = getSuperAdminEmails();

      expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
      expect(Array.isArray(emails)).toBe(true);
    });

    it("should return empty array when no emails configured", () => {
      mockGetSafeEnvironmentConfig.mockReturnValueOnce({
        superAdminEmails: [],
        mcpRulesBase: "https://test.example.com",
        supabaseUrl: "https://test.supabase.co",
        supabaseAnonKey: "test-key",
        googleCloudProject: "test-project",
        mcpProject: "test-mcp",
        tenantProject: "test-tenant",
        useSupabaseAuth: true,
        featureMcpRulesEngine: true,
      });

      const emails = getSuperAdminEmails();
      expect(emails).toEqual([]);
    });
  });

  describe("validateSuperAdminConfig", () => {
    it("should validate correct configuration", () => {
      const result = validateSuperAdminConfig();

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
      expect(result.warnings).toEqual([]);
    });

    it("should detect missing email configuration", () => {
      mockGetSafeEnvironmentConfig.mockReturnValueOnce({
        superAdminEmails: [],
        mcpRulesBase: "https://test.example.com",
        supabaseUrl: "https://test.supabase.co",
        supabaseAnonKey: "test-key",
        googleCloudProject: "test-project",
        mcpProject: "test-mcp",
        tenantProject: "test-tenant",
        useSupabaseAuth: true,
        featureMcpRulesEngine: true,
      });

      const result = validateSuperAdminConfig();

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("No super admin emails configured");
    });

    it("should detect invalid email formats", () => {
      mockGetSafeEnvironmentConfig.mockReturnValueOnce({
        superAdminEmails: ["invalid-email", "<EMAIL>"],
        mcpRulesBase: "https://test.example.com",
        supabaseUrl: "https://test.supabase.co",
        supabaseAnonKey: "test-key",
        googleCloudProject: "test-project",
        mcpProject: "test-mcp",
        tenantProject: "test-tenant",
        useSupabaseAuth: true,
        featureMcpRulesEngine: true,
      });

      const result = validateSuperAdminConfig();

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes("invalid-email"))).toBe(true);
    });

    it("should warn about development environment usage", () => {
      process.env.NODE_ENV = "development";

      const result = validateSuperAdminConfig();

      expect(result.warnings.some(warning => 
        warning.includes("development environment")
      )).toBe(true);
    });
  });

  describe("logSuperAdminConfigStatus", () => {
    it("should log configuration status", () => {
      logSuperAdminConfigStatus();

      expect(console.log).toHaveBeenCalled();
    });

    it("should log errors when configuration is invalid", () => {
      mockGetSafeEnvironmentConfig.mockReturnValueOnce({
        superAdminEmails: [],
        mcpRulesBase: "https://test.example.com",
        supabaseUrl: "https://test.supabase.co",
        supabaseAnonKey: "test-key",
        googleCloudProject: "test-project",
        mcpProject: "test-mcp",
        tenantProject: "test-tenant",
        useSupabaseAuth: true,
        featureMcpRulesEngine: true,
      });

      logSuperAdminConfigStatus();

      expect(console.error).toHaveBeenCalled();
    });

    it("should log warnings when appropriate", () => {
      process.env.NODE_ENV = "development";

      logSuperAdminConfigStatus();

      expect(console.warn).toHaveBeenCalled();
    });
  });

  describe("Security considerations", () => {
    it("should not expose sensitive configuration in logs", () => {
      logSuperAdminConfigStatus();

      // Verify that actual email addresses are not logged
      const logCalls = vi.mocked(console.log).mock.calls;
      const allLoggedContent = logCalls.flat().join(" ");
      
      expect(allLoggedContent).not.toContain("<EMAIL>");
      expect(allLoggedContent).not.toContain("<EMAIL>");
    });

    it("should handle malformed environment configuration gracefully", () => {
      mockGetSafeEnvironmentConfig.mockReturnValueOnce(null as any);

      expect(() => getSuperAdminConfig()).not.toThrow();
    });

    it("should validate email format strictly", () => {
      const invalidEmails = [
        "plainaddress",
        "@missingdomain.com",
        "missing@.com",
        "spaces @domain.com",
        "multiple@@domain.com",
      ];

      mockGetSafeEnvironmentConfig.mockReturnValueOnce({
        superAdminEmails: invalidEmails,
        mcpRulesBase: "https://test.example.com",
        supabaseUrl: "https://test.supabase.co",
        supabaseAnonKey: "test-key",
        googleCloudProject: "test-project",
        mcpProject: "test-mcp",
        tenantProject: "test-tenant",
        useSupabaseAuth: true,
        featureMcpRulesEngine: true,
      });

      const result = validateSuperAdminConfig();

      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe("Environment-specific behavior", () => {
    it("should be more permissive in test environment", () => {
      process.env.NODE_ENV = "test";

      mockGetSafeEnvironmentConfig.mockReturnValueOnce({
        superAdminEmails: [],
        mcpRulesBase: "https://test.example.com",
        supabaseUrl: "https://test.supabase.co",
        supabaseAnonKey: "test-key",
        googleCloudProject: "test-project",
        mcpProject: "test-mcp",
        tenantProject: "test-tenant",
        useSupabaseAuth: true,
        featureMcpRulesEngine: true,
      });

      // Should not throw in test environment
      expect(() => getSuperAdminConfig()).not.toThrow();
    });

    it("should be strict in production environment", () => {
      process.env.NODE_ENV = "production";

      mockGetSafeEnvironmentConfig.mockReturnValueOnce({
        superAdminEmails: [],
        mcpRulesBase: "https://test.example.com",
        supabaseUrl: "https://test.supabase.co",
        supabaseAnonKey: "test-key",
        googleCloudProject: "test-project",
        mcpProject: "test-mcp",
        tenantProject: "test-tenant",
        useSupabaseAuth: true,
        featureMcpRulesEngine: true,
      });

      // Should throw in production environment
      expect(() => getSuperAdminConfig()).toThrow();
    });
  });
});
