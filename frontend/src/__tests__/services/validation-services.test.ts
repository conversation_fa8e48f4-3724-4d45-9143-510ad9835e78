/**
 * Validation Services Unit Tests
 *
 * Comprehensive unit tests for payment validation rules engine,
 * regional validation services, and compliance validation systems.
 */

// Mock environment variables before any imports using hoisted
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL =
    "https://anwefmklplkjxkmzpnva.supabase.co";
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = "test-anon-key";
  process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-key";
  process.env.SENTRY_DSN = "";
  process.env.NODE_ENV = "test";
});

// Mock Sentry to prevent module resolution issues
vi.mock("@sentry/nextjs", () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

import { describe, it, expect, beforeEach, vi } from "vitest";
import { RegionalPaymentMethodService } from "@/lib/services/regional-payment-method-service";
import { PaymentValidationRulesService } from "@/lib/services/payment-validation-rules-service";

// Mock dependencies using hoisted pattern
vi.mock("@/lib/services/regional-payment-method-service", () => ({
  RegionalPaymentMethodService: vi.fn(),
}));

vi.mock("@/lib/services/payment-validation-rules-service", () => ({
  PaymentValidationRulesService: vi.fn(),
}));

// Mock Supabase client
vi.mock("@supabase/supabase-js", () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      insert: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    })),
    auth: {
      getSession: vi.fn(),
      getUser: vi.fn(),
    },
  })),
}));

describe("Regional Payment Method Service", () => {
  let service: RegionalPaymentMethodService;
  let mockSupabase: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockSupabase = {
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
        insert: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
      })),
    };

    // Mock the constructor
    (RegionalPaymentMethodService as any).mockImplementation(() => ({
      getAvailableMethodsForCountry: vi.fn(),
      validatePaymentMethodForRegion: vi.fn(),
      getRegionalRequirements: vi.fn(),
      isMethodSupportedInCountry: vi.fn(),
    }));

    service = new RegionalPaymentMethodService(mockSupabase);
  });

  describe("getAvailableMethodsForCountry", () => {
    it("returns available payment methods for US", async () => {
      const mockMethods = [
        { method: "card", supported: true, required_fields: ["number", "exp_month", "exp_year", "cvc"] },
        { method: "ach_debit", supported: true, required_fields: ["account_number", "routing_number"] },
        { method: "paypal", supported: true, required_fields: [] },
      ];

      service.getAvailableMethodsForCountry = vi.fn().mockResolvedValue(mockMethods);

      const result = await service.getAvailableMethodsForCountry("US");

      expect(result).toEqual(mockMethods);
      expect(service.getAvailableMethodsForCountry).toHaveBeenCalledWith("US");
    });

    it("returns available payment methods for Germany", async () => {
      const mockMethods = [
        { method: "card", supported: true, required_fields: ["number", "exp_month", "exp_year", "cvc"] },
        { method: "sepa_debit", supported: true, required_fields: ["iban"] },
        { method: "sofort", supported: true, required_fields: [] },
        { method: "giropay", supported: true, required_fields: [] },
      ];

      service.getAvailableMethodsForCountry = vi.fn().mockResolvedValue(mockMethods);

      const result = await service.getAvailableMethodsForCountry("DE");

      expect(result).toEqual(mockMethods);
      expect(service.getAvailableMethodsForCountry).toHaveBeenCalledWith("DE");
    });

    it("returns limited methods for unsupported countries", async () => {
      const mockMethods = [
        { method: "card", supported: true, required_fields: ["number", "exp_month", "exp_year", "cvc"] },
      ];

      service.getAvailableMethodsForCountry = vi.fn().mockResolvedValue(mockMethods);

      const result = await service.getAvailableMethodsForCountry("XX");

      expect(result).toEqual(mockMethods);
      expect(result).toHaveLength(1);
      expect(result[0].method).toBe("card");
    });
  });

  describe("validatePaymentMethodForRegion", () => {
    it("validates card payment for US region", async () => {
      const paymentData = {
        method: "card",
        card: {
          number: "****************",
          exp_month: 12,
          exp_year: 2025,
          cvc: "123",
        },
      };

      service.validatePaymentMethodForRegion = vi.fn().mockResolvedValue({
        valid: true,
        errors: [],
      });

      const result = await service.validatePaymentMethodForRegion(paymentData, "US");

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("validates SEPA payment for EU region", async () => {
      const paymentData = {
        method: "sepa_debit",
        sepa_debit: {
          iban: "**********************",
        },
      };

      service.validatePaymentMethodForRegion = vi.fn().mockResolvedValue({
        valid: true,
        errors: [],
      });

      const result = await service.validatePaymentMethodForRegion(paymentData, "DE");

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("returns validation errors for invalid payment data", async () => {
      const paymentData = {
        method: "card",
        card: {
          number: "invalid",
          exp_month: 13, // Invalid month
          exp_year: 2020, // Expired year
          cvc: "12", // Too short
        },
      };

      service.validatePaymentMethodForRegion = vi.fn().mockResolvedValue({
        valid: false,
        errors: [
          "Invalid card number",
          "Invalid expiration month",
          "Card has expired",
          "Invalid CVC length",
        ],
      });

      const result = await service.validatePaymentMethodForRegion(paymentData, "US");

      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(4);
    });
  });

  describe("getRegionalRequirements", () => {
    it("returns US regional requirements", async () => {
      const mockRequirements = {
        country: "US",
        currency: "USD",
        tax_calculation: "required",
        compliance_level: "standard",
        supported_methods: ["card", "ach_debit", "paypal"],
        required_fields: {
          billing_address: true,
          phone_number: false,
          tax_id: false,
        },
      };

      service.getRegionalRequirements = vi.fn().mockResolvedValue(mockRequirements);

      const result = await service.getRegionalRequirements("US");

      expect(result).toEqual(mockRequirements);
      expect(result.currency).toBe("USD");
      expect(result.supported_methods).toContain("card");
    });

    it("returns EU regional requirements", async () => {
      const mockRequirements = {
        country: "DE",
        currency: "EUR",
        tax_calculation: "required",
        compliance_level: "strict",
        supported_methods: ["card", "sepa_debit", "sofort", "giropay"],
        required_fields: {
          billing_address: true,
          phone_number: true,
          tax_id: true,
        },
      };

      service.getRegionalRequirements = vi.fn().mockResolvedValue(mockRequirements);

      const result = await service.getRegionalRequirements("DE");

      expect(result).toEqual(mockRequirements);
      expect(result.currency).toBe("EUR");
      expect(result.compliance_level).toBe("strict");
    });
  });

  describe("isMethodSupportedInCountry", () => {
    it("returns true for supported method in country", async () => {
      service.isMethodSupportedInCountry = vi.fn().mockResolvedValue(true);

      const result = await service.isMethodSupportedInCountry("card", "US");

      expect(result).toBe(true);
    });

    it("returns false for unsupported method in country", async () => {
      service.isMethodSupportedInCountry = vi.fn().mockResolvedValue(false);

      const result = await service.isMethodSupportedInCountry("alipay", "US");

      expect(result).toBe(false);
    });
  });
});

describe("Payment Validation Rules Service", () => {
  let service: PaymentValidationRulesService;
  let mockSupabase: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockSupabase = {
      from: vi.fn(() => ({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
        insert: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
      })),
    };

    // Mock the constructor
    (PaymentValidationRulesService as any).mockImplementation(() => ({
      validateAmount: vi.fn(),
      validateCurrency: vi.fn(),
      validatePaymentMethod: vi.fn(),
      getValidationRules: vi.fn(),
    }));

    service = new PaymentValidationRulesService(mockSupabase);
  });

  describe("validateAmount", () => {
    it("validates amount within limits", async () => {
      service.validateAmount = vi.fn().mockResolvedValue({
        valid: true,
        errors: [],
      });

      const result = await service.validateAmount(100.00, "USD");

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("rejects amount below minimum", async () => {
      service.validateAmount = vi.fn().mockResolvedValue({
        valid: false,
        errors: ["Amount below minimum threshold"],
      });

      const result = await service.validateAmount(0.50, "USD");

      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Amount below minimum threshold");
    });

    it("rejects amount above maximum", async () => {
      service.validateAmount = vi.fn().mockResolvedValue({
        valid: false,
        errors: ["Amount exceeds maximum threshold"],
      });

      const result = await service.validateAmount(100000.00, "USD");

      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Amount exceeds maximum threshold");
    });
  });

  describe("validateCurrency", () => {
    it("validates supported currency", async () => {
      service.validateCurrency = vi.fn().mockResolvedValue({
        valid: true,
        errors: [],
      });

      const result = await service.validateCurrency("USD");

      expect(result.valid).toBe(true);
    });

    it("rejects unsupported currency", async () => {
      service.validateCurrency = vi.fn().mockResolvedValue({
        valid: false,
        errors: ["Currency not supported"],
      });

      const result = await service.validateCurrency("XYZ");

      expect(result.valid).toBe(false);
      expect(result.errors).toContain("Currency not supported");
    });
  });

  describe("validatePaymentMethod", () => {
    it("validates card payment method", async () => {
      const paymentData = {
        method: "card",
        card: {
          number: "****************",
          exp_month: 12,
          exp_year: 2025,
          cvc: "123",
        },
      };

      service.validatePaymentMethod = vi.fn().mockResolvedValue({
        valid: true,
        errors: [],
        formatted_data: paymentData,
      });

      const result = await service.validatePaymentMethod(paymentData);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.formatted_data).toEqual(paymentData);
    });

    it("validates SEPA payment method", async () => {
      const paymentData = {
        method: "sepa_debit",
        sepa_debit: {
          iban: "**********************",
          account_holder_name: "John Doe",
        },
      };

      service.validatePaymentMethod = vi.fn().mockResolvedValue({
        valid: true,
        errors: [],
        formatted_data: paymentData,
      });

      const result = await service.validatePaymentMethod(paymentData);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("rejects invalid payment method data", async () => {
      const paymentData = {
        method: "card",
        card: {
          number: "invalid",
          exp_month: 13,
          exp_year: 2020,
          cvc: "12",
        },
      };

      service.validatePaymentMethod = vi.fn().mockResolvedValue({
        valid: false,
        errors: [
          "Invalid card number",
          "Invalid expiration month",
          "Card has expired",
          "Invalid CVC length",
        ],
        formatted_data: null,
      });

      const result = await service.validatePaymentMethod(paymentData);

      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(4);
      expect(result.formatted_data).toBeNull();
    });
  });

  describe("getValidationRules", () => {
    it("returns validation rules for country and currency", async () => {
      const mockRules = {
        country: "US",
        currency: "USD",
        amount_limits: {
          minimum_cents: 50,
          maximum_cents: 10000000,
        },
        supported_methods: ["card", "ach_debit", "paypal"],
        required_fields: {
          billing_address: true,
          phone_number: false,
          tax_id: false,
        },
      };

      service.getValidationRules = vi.fn().mockResolvedValue(mockRules);

      const result = await service.getValidationRules("US", "USD");

      expect(result).toEqual(mockRules);
      expect(result.supported_methods).toContain("card");
      expect(result.amount_limits.minimum_cents).toBe(50);
    });
  });
});
