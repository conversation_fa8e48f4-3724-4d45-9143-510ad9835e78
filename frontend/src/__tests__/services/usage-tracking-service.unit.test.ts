import { describe, it, expect, beforeEach, vi } from "vitest";

// Mock environment variables before any imports using hoisted
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL =
    "https://anwefmklplkjxkmzpnva.supabase.co";
  process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-key";
  process.env.SENTRY_DSN = "";
  process.env.NODE_ENV = "test";
});

// Mock Sentry to prevent module resolution issues
vi.mock("@sentry/nextjs", () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

import { UsageTrackingService } from "@/lib/services/usage-tracking-service";

// Mock Supabase client with schema support
const mockSupabase = {
  from: vi.fn(),
  rpc: vi.fn(),
  schema: vi.fn(),
};

describe("UsageTrackingService", () => {
  let usageTrackingService: UsageTrackingService;

  beforeEach(() => {
    vi.clearAllMocks();

    // Set up mock chain for database operations
    const mockChain = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      lte: vi.fn().mockReturnThis(),
      gt: vi.fn().mockReturnThis(),
      lt: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      upsert: vi.fn().mockReturnThis(),
    };

    // Set up mockSupabase methods
    mockSupabase.from.mockReturnValue(mockChain);
    mockSupabase.rpc.mockResolvedValue({ data: null, error: null });
    mockSupabase.schema.mockReturnValue({
      from: vi.fn().mockReturnValue(mockChain),
    });

    usageTrackingService = new UsageTrackingService(mockSupabase as any);
  });

  describe("trackResourceUsage", () => {
    it("should track resource usage correctly", async () => {
      // Mock data
      const mockUsage = {
        id: "usage-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 5,
        resource_size_bytes: 1024,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      // Setup mock
      mockSupabase.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: mockUsage, error: null }),
          }),
        }),
      });

      // Mock Date.now
      const realDate = Date;
      global.Date = class extends Date {
        constructor() {
          super("2023-01-15T12:00:00Z");
        }
        static now() {
          return new Date("2023-01-15T12:00:00Z").getTime();
        }
      } as any;

      // Call the method
      const result = await usageTrackingService.trackResourceUsage({
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 5,
        resourceSizeBytes: 1024,
      });

      // Restore Date
      global.Date = realDate;

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        id: "usage-1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 5,
        resourceSizeBytes: 1024,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });
    });
  });

  describe("incrementResourceUsage", () => {
    it("should update existing usage record", async () => {
      // Mock data for existing usage
      const mockExistingUsage = {
        id: "usage-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 5,
        resource_size_bytes: 1024,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      // Mock data for updated usage
      const mockUpdatedUsage = {
        id: "usage-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 7, // Incremented by 2
        resource_size_bytes: 2048, // Incremented by 1024
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      // Setup mock for finding existing usage
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === "resource_usage") {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  gte: vi.fn().mockReturnValue({
                    lte: vi.fn().mockReturnValue({
                      single: vi.fn().mockResolvedValue({
                        data: mockExistingUsage,
                        error: null,
                      }),
                    }),
                  }),
                }),
              }),
            }),
            update: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                select: vi.fn().mockReturnValue({
                  single: vi
                    .fn()
                    .mockResolvedValue({ data: mockUpdatedUsage, error: null }),
                }),
              }),
            }),
          };
        }
        return {};
      });

      // Mock Date.now
      const realDate = Date;
      global.Date = class extends Date {
        constructor() {
          super("2023-01-15T12:00:00Z");
        }
        static now() {
          return new Date("2023-01-15T12:00:00Z").getTime();
        }
      } as any;

      // Call the method
      const result = await usageTrackingService.incrementResourceUsage({
        tenantId: "tenant-1",
        usageType: "document_upload",
        incrementBy: 2,
        resourceSizeBytes: 1024,
      });

      // Restore Date
      global.Date = realDate;

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        id: "usage-1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 7,
        resourceSizeBytes: 2048,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });
    });

    it("should create new usage record when none exists", async () => {
      // Mock data for new usage
      const mockNewUsage = {
        id: "usage-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 2,
        resource_size_bytes: 1024,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      // Setup mock for finding no existing usage
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === "resource_usage") {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  gte: vi.fn().mockReturnValue({
                    lte: vi.fn().mockReturnValue({
                      single: vi.fn().mockResolvedValue({
                        data: null,
                        error: { code: "PGRST116" },
                      }),
                    }),
                  }),
                }),
              }),
            }),
            insert: vi.fn().mockReturnValue({
              select: vi.fn().mockReturnValue({
                single: vi
                  .fn()
                  .mockResolvedValue({ data: mockNewUsage, error: null }),
              }),
            }),
          };
        }
        return {};
      });

      // Mock trackResourceUsage
      vi.spyOn(usageTrackingService, "trackResourceUsage").mockResolvedValue({
        id: "usage-1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 2,
        resourceSizeBytes: 1024,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });

      // Mock Date.now
      const realDate = Date;
      global.Date = class extends Date {
        constructor() {
          super("2023-01-15T12:00:00Z");
        }
        static now() {
          return new Date("2023-01-15T12:00:00Z").getTime();
        }
      } as any;

      // Call the method
      const result = await usageTrackingService.incrementResourceUsage({
        tenantId: "tenant-1",
        usageType: "document_upload",
        incrementBy: 2,
        resourceSizeBytes: 1024,
      });

      // Restore Date
      global.Date = realDate;

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(usageTrackingService.trackResourceUsage).toHaveBeenCalled();
      expect(result).toEqual({
        id: "usage-1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 2,
        resourceSizeBytes: 1024,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });
    });
  });

  describe("getCurrentPeriodUsage", () => {
    it("should return current period usage", async () => {
      // Mock data
      const mockUsage = {
        id: "usage-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 5,
        resource_size_bytes: 1024,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      // Setup mock
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  single: vi
                    .fn()
                    .mockResolvedValue({ data: mockUsage, error: null }),
                }),
              }),
            }),
          }),
        }),
      });

      // Mock Date.now
      const realDate = Date;
      global.Date = class extends Date {
        constructor() {
          super("2023-01-15T12:00:00Z");
        }
        static now() {
          return new Date("2023-01-15T12:00:00Z").getTime();
        }
      } as any;

      // Call the method
      const result = await usageTrackingService.getCurrentPeriodUsage(
        "tenant-1",
        "document_upload",
      );

      // Restore Date
      global.Date = realDate;

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        id: "usage-1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 5,
        resourceSizeBytes: 1024,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });
    });

    it("should return null when no usage exists", async () => {
      // Setup mock
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  single: vi.fn().mockResolvedValue({
                    data: null,
                    error: { code: "PGRST116" },
                  }),
                }),
              }),
            }),
          }),
        }),
      });

      // Call the method
      const result = await usageTrackingService.getCurrentPeriodUsage(
        "tenant-1",
        "document_upload",
      );

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toBeNull();
    });
  });

  describe("checkQuotaLimit", () => {
    it("should return quota check result when within quota", async () => {
      // Mock data for tenant quota
      const mockQuota = {
        id: "quota-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        quota_limit: 100,
        reset_frequency: "monthly",
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      };

      // Mock data for current usage
      const mockUsage = {
        id: "usage-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 50,
        resource_size_bytes: 1024,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      // Setup mocks
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === "tenant_quotas") {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                single: vi
                  .fn()
                  .mockResolvedValue({ data: mockQuota, error: null }),
              }),
            }),
          };
        } else if (table === "resource_usage") {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  gte: vi.fn().mockReturnValue({
                    lte: vi.fn().mockReturnValue({
                      single: vi
                        .fn()
                        .mockResolvedValue({ data: mockUsage, error: null }),
                    }),
                  }),
                }),
              }),
            }),
          };
        }
        return {};
      });

      // Call the method
      const result = await usageTrackingService.checkQuotaLimit({
        tenantId: "tenant-1",
        usageType: "document_upload",
        incrementBy: 10,
      });

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith("tenant_quotas");
      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        withinQuota: true,
        currentUsage: 50,
        quotaLimit: 100,
        percentUsed: 50,
      });
    });

    it("should return quota check result when exceeding quota", async () => {
      // Mock data for tenant quota
      const mockQuota = {
        id: "quota-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        quota_limit: 100,
        reset_frequency: "monthly",
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      };

      // Mock data for current usage
      const mockUsage = {
        id: "usage-1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 95,
        resource_size_bytes: 1024,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      // Setup mocks
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === "tenant_quotas") {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                single: vi
                  .fn()
                  .mockResolvedValue({ data: mockQuota, error: null }),
              }),
            }),
          };
        } else if (table === "resource_usage") {
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  gte: vi.fn().mockReturnValue({
                    lte: vi.fn().mockReturnValue({
                      single: vi
                        .fn()
                        .mockResolvedValue({ data: mockUsage, error: null }),
                    }),
                  }),
                }),
              }),
            }),
          };
        }
        return {};
      });

      // Call the method
      const result = await usageTrackingService.checkQuotaLimit({
        tenantId: "tenant-1",
        usageType: "document_upload",
        incrementBy: 10,
      });

      // Assertions
      expect(mockSupabase.from).toHaveBeenCalledWith("tenant_quotas");
      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        withinQuota: false,
        currentUsage: 95,
        quotaLimit: 100,
        percentUsed: 95,
      });
    });
  });
});
