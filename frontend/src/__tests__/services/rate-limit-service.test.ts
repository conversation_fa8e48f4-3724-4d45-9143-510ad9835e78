/**
 * Rate Limit Service Tests
 * 
 * Tests for rate limiting functionality including document upload limits,
 * processing throttling, and time-based window management.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { RateLimitService } from "@/lib/services/rate-limit-service";
import { 
  setupFakeTimers, 
  cleanupFakeTimers, 
  advanceTimeByMinutes,
  advanceTimeByHours,
  testSlidingWindow,
  testFixedWindow,
  TIME_CONSTANTS
} from "@/test/helpers/time-control";

// Mock the rate limit service
const mockRateLimitService = {
  canUploadDocument: vi.fn(),
  trackDocumentUpload: vi.fn(),
  trackProcessingStart: vi.fn(),
  trackProcessingComplete: vi.fn(),
  getRemainingQuota: vi.fn(),
  getWindowResetTime: vi.fn(),
};

vi.mock("@/lib/services/rate-limit-service", () => ({
  RateLimitService: vi.fn(() => mockRateLimitService),
}));

describe("RateLimitService", () => {
  let rateLimitService: any;
  const TEST_TENANT_ID = "123e4567-e89b-12d3-a456-426614174000";

  beforeEach(() => {
    vi.clearAllMocks();
    setupFakeTimers('2025-01-01T00:00:00Z');
    
    rateLimitService = new RateLimitService();
  });

  afterEach(() => {
    cleanupFakeTimers();
  });

  describe("canUploadDocument", () => {
    it("should allow upload when under rate limit", async () => {
      mockRateLimitService.canUploadDocument.mockResolvedValue({
        allowed: true,
        remainingQuota: 45,
        resetTime: new Date(Date.now() + TIME_CONSTANTS.HOUR),
      });

      const result = await rateLimitService.canUploadDocument(TEST_TENANT_ID);

      expect(result.allowed).toBe(true);
      expect(result.remainingQuota).toBe(45);
      expect(mockRateLimitService.canUploadDocument).toHaveBeenCalledWith(TEST_TENANT_ID);
    });

    it("should deny upload when rate limit exceeded", async () => {
      mockRateLimitService.canUploadDocument.mockResolvedValue({
        allowed: false,
        remainingQuota: 0,
        resetTime: new Date(Date.now() + TIME_CONSTANTS.HOUR),
        message: "Rate limit exceeded",
      });

      const result = await rateLimitService.canUploadDocument(TEST_TENANT_ID);

      expect(result.allowed).toBe(false);
      expect(result.remainingQuota).toBe(0);
      expect(result.message).toBe("Rate limit exceeded");
    });

    it("should reset quota after time window", async () => {
      // First, exceed the limit
      mockRateLimitService.canUploadDocument.mockResolvedValueOnce({
        allowed: false,
        remainingQuota: 0,
        resetTime: new Date(Date.now() + TIME_CONSTANTS.HOUR),
      });

      let result = await rateLimitService.canUploadDocument(TEST_TENANT_ID);
      expect(result.allowed).toBe(false);

      // Advance time past the reset window
      advanceTimeByHours(1);

      // Should allow uploads again
      mockRateLimitService.canUploadDocument.mockResolvedValueOnce({
        allowed: true,
        remainingQuota: 50,
        resetTime: new Date(Date.now() + TIME_CONSTANTS.HOUR),
      });

      result = await rateLimitService.canUploadDocument(TEST_TENANT_ID);
      expect(result.allowed).toBe(true);
      expect(result.remainingQuota).toBe(50);
    });
  });

  describe("trackDocumentUpload", () => {
    it("should track successful document upload", async () => {
      const documentId = "doc-123";
      const uploadSize = 1024 * 1024; // 1MB

      mockRateLimitService.trackDocumentUpload.mockResolvedValue({
        success: true,
        newQuotaUsed: 1,
        remainingQuota: 49,
      });

      const result = await rateLimitService.trackDocumentUpload(
        TEST_TENANT_ID,
        documentId,
        uploadSize
      );

      expect(result.success).toBe(true);
      expect(result.newQuotaUsed).toBe(1);
      expect(mockRateLimitService.trackDocumentUpload).toHaveBeenCalledWith(
        TEST_TENANT_ID,
        documentId,
        uploadSize
      );
    });

    it("should handle tracking errors gracefully", async () => {
      const documentId = "doc-error";
      const uploadSize = 1024;

      mockRateLimitService.trackDocumentUpload.mockRejectedValue(
        new Error("Database error")
      );

      await expect(
        rateLimitService.trackDocumentUpload(TEST_TENANT_ID, documentId, uploadSize)
      ).rejects.toThrow("Database error");
    });
  });

  describe("trackProcessingStart", () => {
    it("should track processing start time", async () => {
      const documentId = "doc-processing";

      mockRateLimitService.trackProcessingStart.mockResolvedValue({
        success: true,
        startTime: new Date(),
      });

      const result = await rateLimitService.trackProcessingStart(
        TEST_TENANT_ID,
        documentId
      );

      expect(result.success).toBe(true);
      expect(result.startTime).toBeInstanceOf(Date);
      expect(mockRateLimitService.trackProcessingStart).toHaveBeenCalledWith(
        TEST_TENANT_ID,
        documentId
      );
    });
  });

  describe("trackProcessingComplete", () => {
    it("should track processing completion", async () => {
      const documentId = "doc-complete";
      const processingTimeMs = 5000;

      mockRateLimitService.trackProcessingComplete.mockResolvedValue({
        success: true,
        processingTime: processingTimeMs,
        endTime: new Date(),
      });

      const result = await rateLimitService.trackProcessingComplete(
        TEST_TENANT_ID,
        documentId
      );

      expect(result.success).toBe(true);
      expect(result.processingTime).toBe(processingTimeMs);
      expect(mockRateLimitService.trackProcessingComplete).toHaveBeenCalledWith(
        TEST_TENANT_ID,
        documentId
      );
    });
  });

  describe("Sliding window rate limiting", () => {
    it("should implement sliding window correctly", async () => {
      const windowSizeMs = TIME_CONSTANTS.HOUR;
      const maxRequests = 50;

      // Mock the service to simulate sliding window behavior
      let requestCount = 0;
      mockRateLimitService.canUploadDocument.mockImplementation(async () => {
        requestCount++;
        if (requestCount <= maxRequests) {
          return {
            allowed: true,
            remainingQuota: maxRequests - requestCount,
            resetTime: new Date(Date.now() + windowSizeMs),
          };
        } else {
          return {
            allowed: false,
            remainingQuota: 0,
            resetTime: new Date(Date.now() + windowSizeMs),
            message: "Rate limit exceeded",
          };
        }
      });

      // Test sliding window behavior
      const requestFn = () => rateLimitService.canUploadDocument(TEST_TENANT_ID);
      
      const results = await testSlidingWindow(requestFn, windowSizeMs, maxRequests);

      expect(results.initialRequests).toHaveLength(maxRequests);
      expect(results.initialRequests.every(r => r.allowed)).toBe(true);
      expect(results.exceedsLimit.allowed).toBe(false);
      expect(results.afterWindowSlide).toHaveLength(maxRequests);
    });
  });

  describe("Fixed window rate limiting", () => {
    it("should implement fixed window correctly", async () => {
      const windowSizeMs = TIME_CONSTANTS.HOUR;
      const maxRequests = 50;

      // Mock the service to simulate fixed window behavior
      let requestCount = 0;
      let windowStart = Date.now();

      mockRateLimitService.canUploadDocument.mockImplementation(async () => {
        const now = Date.now();
        
        // Reset counter if we're in a new window
        if (now - windowStart >= windowSizeMs) {
          requestCount = 0;
          windowStart = now;
        }

        requestCount++;
        
        if (requestCount <= maxRequests) {
          return {
            allowed: true,
            remainingQuota: maxRequests - requestCount,
            resetTime: new Date(windowStart + windowSizeMs),
          };
        } else {
          return {
            allowed: false,
            remainingQuota: 0,
            resetTime: new Date(windowStart + windowSizeMs),
            message: "Rate limit exceeded",
          };
        }
      });

      const requestFn = () => rateLimitService.canUploadDocument(TEST_TENANT_ID);
      
      const results = await testFixedWindow(requestFn, windowSizeMs, maxRequests);

      expect(results.withinWindow).toHaveLength(maxRequests);
      expect(results.withinWindow.every(r => r.allowed)).toBe(true);
      expect(results.exceedsLimit.allowed).toBe(false);
      expect(results.nextWindow).toHaveLength(maxRequests);
    });
  });

  describe("getRemainingQuota", () => {
    it("should return current quota information", async () => {
      mockRateLimitService.getRemainingQuota.mockResolvedValue({
        remainingQuota: 25,
        totalQuota: 50,
        resetTime: new Date(Date.now() + TIME_CONSTANTS.HOUR),
        windowType: "sliding",
      });

      const result = await rateLimitService.getRemainingQuota(TEST_TENANT_ID);

      expect(result.remainingQuota).toBe(25);
      expect(result.totalQuota).toBe(50);
      expect(result.windowType).toBe("sliding");
      expect(mockRateLimitService.getRemainingQuota).toHaveBeenCalledWith(TEST_TENANT_ID);
    });
  });

  describe("getWindowResetTime", () => {
    it("should return next window reset time", async () => {
      const resetTime = new Date(Date.now() + TIME_CONSTANTS.HOUR);
      
      mockRateLimitService.getWindowResetTime.mockResolvedValue({
        resetTime,
        timeUntilReset: TIME_CONSTANTS.HOUR,
      });

      const result = await rateLimitService.getWindowResetTime(TEST_TENANT_ID);

      expect(result.resetTime).toEqual(resetTime);
      expect(result.timeUntilReset).toBe(TIME_CONSTANTS.HOUR);
      expect(mockRateLimitService.getWindowResetTime).toHaveBeenCalledWith(TEST_TENANT_ID);
    });
  });

  describe("Time-based scenarios", () => {
    it("should handle quota refresh at exact window boundary", async () => {
      // Set up initial state at window boundary
      mockRateLimitService.canUploadDocument.mockResolvedValueOnce({
        allowed: false,
        remainingQuota: 0,
        resetTime: new Date(Date.now() + 1), // Reset in 1ms
      });

      let result = await rateLimitService.canUploadDocument(TEST_TENANT_ID);
      expect(result.allowed).toBe(false);

      // Advance time to exactly the reset boundary
      advanceTimeByMinutes(0); // Advance by 1ms would be more precise
      
      mockRateLimitService.canUploadDocument.mockResolvedValueOnce({
        allowed: true,
        remainingQuota: 50,
        resetTime: new Date(Date.now() + TIME_CONSTANTS.HOUR),
      });

      result = await rateLimitService.canUploadDocument(TEST_TENANT_ID);
      expect(result.allowed).toBe(true);
    });

    it("should handle multiple tenants independently", async () => {
      const tenant1 = "tenant-1";
      const tenant2 = "tenant-2";

      // Tenant 1 exceeds limit
      mockRateLimitService.canUploadDocument.mockImplementation(async (tenantId) => {
        if (tenantId === tenant1) {
          return { allowed: false, remainingQuota: 0 };
        } else {
          return { allowed: true, remainingQuota: 50 };
        }
      });

      const result1 = await rateLimitService.canUploadDocument(tenant1);
      const result2 = await rateLimitService.canUploadDocument(tenant2);

      expect(result1.allowed).toBe(false);
      expect(result2.allowed).toBe(true);
    });
  });
});
