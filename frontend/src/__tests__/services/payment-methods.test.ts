/**
 * Payment Methods API Tests
 *
 * Comprehensive test suite for payment method API endpoints
 * including unit tests, integration tests, and error scenarios.
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import jwt from "jsonwebtoken";
import { NextRequest } from "next/server";
import {
  createMockUser,
  createSuccessfulPaymentAuth,
  createUnauthorizedPaymentAuth,
  createInsufficientPermissionsAuth,
  createRateLimitedAuth,
  overridePaymentAuth,
  resetPaymentAuthToSuccess,
} from "@/test/helpers/auth-helpers";

// Mock environment variables and Sentry before any imports using hoisted
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = "https://test.supabase.co";
  process.env.SUPABASE_SERVICE_ROLE_KEY = "test-service-key";
  process.env.SUPABASE_JWT_SECRET = "test-jwt-secret-key-32-characters";
  process.env.STRIPE_SECRET_KEY = "sk_test_123";
  process.env.STRIPE_PUBLISHABLE_KEY = "pk_test_123";
  process.env.PAYMENT_API_KEY = "test-payment-api-key";
  process.env.SENTRY_DSN = "";
  process.env.NODE_ENV = "test";
});

// Mock Sentry to prevent module resolution issues
vi.mock("@sentry/nextjs", () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

// Mock rate limiter to avoid flakes in tests
vi.mock("@/lib/middlewares/rate-limit", () => ({
  rateLimit: vi.fn().mockResolvedValue({
    success: true,
    current: 1,
    limit: 100,
    retryAfter: 0,
  }),
}));

// Mock services with predictable defaults
vi.mock("@/lib/services/regional-payment-method-service", () => ({
  RegionalPaymentMethodService: vi.fn().mockImplementation(() => ({
    getAvailablePaymentMethods: vi.fn().mockResolvedValue([{ code: "card" }]),
    getRecommendedPaymentMethod: vi.fn().mockResolvedValue({ code: "card" }),
    validatePaymentMethod: vi.fn().mockResolvedValue({ valid: true }),
  })),
}));

vi.mock("@/lib/services/stripe-payment-method-service", () => ({
  StripePaymentMethodService: vi.fn().mockImplementation(() => ({
    createPaymentMethod: vi.fn().mockResolvedValue({ id: "pm_test_123" }),
    attachPaymentMethod: vi.fn().mockResolvedValue({ success: true }),
    detachPaymentMethod: vi.fn().mockResolvedValue({ success: true }),
    listPaymentMethods: vi.fn().mockResolvedValue([]),
  })),
}));

// Mock payment auth with default success
vi.mock("@/lib/auth/payment-auth", () => ({
  authorizeRequest: vi.fn().mockResolvedValue(createSuccessfulPaymentAuth()),
}));

// Mock API handlers
const mockPaymentMethodsHandler = vi.fn();
vi.mock("@/app/api/payment-methods/route", () => ({
  GET: mockPaymentMethodsHandler,
  POST: mockPaymentMethodsHandler,
}));

describe("Payment Methods API", () => {
  const mockUser = createMockUser({
    id: "user-123",
    email: "<EMAIL>",
    tenant_id: "tenant-123",
  });

  beforeEach(async () => {
    vi.clearAllMocks();
    await resetPaymentAuthToSuccess(mockUser, { id: "tenant-123" });
  });

  describe("GET /api/payment-methods", () => {
    it("should return available payment methods for authenticated user", async () => {
      const mockPaymentMethods = [
        { code: "card", name: "Credit Card", enabled: true },
        { code: "bank_transfer", name: "Bank Transfer", enabled: true },
      ];

      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ paymentMethods: mockPaymentMethods }), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "GET",
        headers: {
          Authorization: "Bearer valid-jwt-token",
        },
      });

      const response = await mockPaymentMethodsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.paymentMethods).toEqual(mockPaymentMethods);
    });

    it("should return 401 for unauthenticated requests", async () => {
      await overridePaymentAuth(createUnauthorizedPaymentAuth());

      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ error: "Unauthorized" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "GET",
      });

      const response = await mockPaymentMethodsHandler(request);

      expect(response.status).toBe(401);
    });

    it("should return 403 for insufficient permissions", async () => {
      await overridePaymentAuth(createInsufficientPermissionsAuth());

      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ error: "Insufficient permissions" }), {
          status: 403,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "GET",
        headers: {
          Authorization: "Bearer valid-jwt-token",
        },
      });

      const response = await mockPaymentMethodsHandler(request);

      expect(response.status).toBe(403);
    });

    it("should return 429 for rate limited requests", async () => {
      await overridePaymentAuth(createRateLimitedAuth());

      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ error: "Rate limit exceeded" }), {
          status: 429,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "GET",
        headers: {
          Authorization: "Bearer valid-jwt-token",
        },
      });

      const response = await mockPaymentMethodsHandler(request);

      expect(response.status).toBe(429);
    });

    it("should handle regional payment method filtering", async () => {
      const mockRegionalMethods = [
        { code: "card", name: "Credit Card", enabled: true, regions: ["US", "EU"] },
        { code: "sepa", name: "SEPA", enabled: true, regions: ["EU"] },
        { code: "ach", name: "ACH", enabled: true, regions: ["US"] },
      ];

      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ paymentMethods: mockRegionalMethods }), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods?region=EU", {
        method: "GET",
        headers: {
          Authorization: "Bearer valid-jwt-token",
        },
      });

      const response = await mockPaymentMethodsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.paymentMethods).toEqual(mockRegionalMethods);
    });
  });

  describe("POST /api/payment-methods", () => {
    it("should create a new payment method", async () => {
      const mockCreatedMethod = {
        id: "pm_test_123",
        type: "card",
        card: {
          brand: "visa",
          last4: "4242",
        },
      };

      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ paymentMethod: mockCreatedMethod }), {
          status: 201,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "POST",
        headers: {
          Authorization: "Bearer valid-jwt-token",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "card",
          card: {
            number: "****************",
            exp_month: 12,
            exp_year: 2025,
            cvc: "123",
          },
        }),
      });

      const response = await mockPaymentMethodsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.paymentMethod).toEqual(mockCreatedMethod);
    });

    it("should validate payment method data", async () => {
      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ error: "Invalid payment method data" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "POST",
        headers: {
          Authorization: "Bearer valid-jwt-token",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "card",
          // Missing required card data
        }),
      });

      const response = await mockPaymentMethodsHandler(request);

      expect(response.status).toBe(400);
    });

    it("should handle Stripe API errors", async () => {
      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ error: "Your card was declined" }), {
          status: 402,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "POST",
        headers: {
          Authorization: "Bearer valid-jwt-token",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "card",
          card: {
            number: "****************", // Declined card
            exp_month: 12,
            exp_year: 2025,
            cvc: "123",
          },
        }),
      });

      const response = await mockPaymentMethodsHandler(request);

      expect(response.status).toBe(402);
    });

    it("should return 401 for unauthenticated creation attempts", async () => {
      await overridePaymentAuth(createUnauthorizedPaymentAuth());

      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ error: "Unauthorized" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "card",
          card: {
            number: "****************",
            exp_month: 12,
            exp_year: 2025,
            cvc: "123",
          },
        }),
      });

      const response = await mockPaymentMethodsHandler(request);

      expect(response.status).toBe(401);
    });
  });

  describe("Error handling", () => {
    it("should handle malformed JSON requests", async () => {
      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ error: "Invalid JSON" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "POST",
        headers: {
          Authorization: "Bearer valid-jwt-token",
          "Content-Type": "application/json",
        },
        body: "invalid-json",
      });

      const response = await mockPaymentMethodsHandler(request);

      expect(response.status).toBe(400);
    });

    it("should handle internal server errors", async () => {
      mockPaymentMethodsHandler.mockResolvedValue(
        new Response(JSON.stringify({ error: "Internal server error" }), {
          status: 500,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request = new NextRequest("https://example.com/api/payment-methods", {
        method: "GET",
        headers: {
          Authorization: "Bearer valid-jwt-token",
        },
      });

      const response = await mockPaymentMethodsHandler(request);

      expect(response.status).toBe(500);
    });
  });

  describe("Multi-tenant isolation", () => {
    it("should isolate payment methods by tenant", async () => {
      const tenant1Methods = [{ id: "pm_tenant1_123", tenant_id: "tenant-1" }];
      const tenant2Methods = [{ id: "pm_tenant2_456", tenant_id: "tenant-2" }];

      // Test tenant 1
      await resetPaymentAuthToSuccess(
        createMockUser({ tenant_id: "tenant-1" }),
        { id: "tenant-1" }
      );

      mockPaymentMethodsHandler.mockResolvedValueOnce(
        new Response(JSON.stringify({ paymentMethods: tenant1Methods }), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request1 = new NextRequest("https://example.com/api/payment-methods", {
        method: "GET",
        headers: {
          Authorization: "Bearer tenant1-jwt-token",
        },
      });

      const response1 = await mockPaymentMethodsHandler(request1);
      const data1 = await response1.json();

      expect(data1.paymentMethods).toEqual(tenant1Methods);

      // Test tenant 2
      await resetPaymentAuthToSuccess(
        createMockUser({ tenant_id: "tenant-2" }),
        { id: "tenant-2" }
      );

      mockPaymentMethodsHandler.mockResolvedValueOnce(
        new Response(JSON.stringify({ paymentMethods: tenant2Methods }), {
          status: 200,
          headers: { "Content-Type": "application/json" },
        })
      );

      const request2 = new NextRequest("https://example.com/api/payment-methods", {
        method: "GET",
        headers: {
          Authorization: "Bearer tenant2-jwt-token",
        },
      });

      const response2 = await mockPaymentMethodsHandler(request2);
      const data2 = await response2.json();

      expect(data2.paymentMethods).toEqual(tenant2Methods);
    });
  });
});
