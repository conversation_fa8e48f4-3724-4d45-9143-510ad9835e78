/**
 * Test suite for the QueueService class which handles document processing queue operations.
 * This service is responsible for enqueueing documents for processing, checking job statuses,
 * retrieving all jobs for a document, and reprocessing documents.
 */

import { describe, it, expect, beforeEach, vi } from "vitest";

// Mock the entire queue service
const mockEnqueueDocument = vi.fn();
const mockGetJobStatus = vi.fn();
const mockGetDocumentJobs = vi.fn();
const mockReprocessDocument = vi.fn();

vi.mock("@/lib/services/queue-service", () => {
  return {
    QueueService: vi.fn().mockImplementation(() => {
      return {
        enqueueDocument: mockEnqueueDocument,
        getJobStatus: mockGetJobStatus,
        getDocumentJobs: mockGetDocumentJobs,
        reprocessDocument: mockReprocessDocument,
      };
    }),
  };
});

describe("QueueService", () => {
  let queueService: any;
  const tenantId = "test-tenant-123";

  beforeEach(() => {
    // Reset all mocks before each test to ensure clean state
    vi.clearAllMocks();

    // Use the centralized Supabase mock from vitest.setup.ts
    const { QueueService } = require("@/lib/services/queue-service");
    queueService = new QueueService();
  });

  /**
   * Tests for the enqueueDocument method which adds documents to the processing queue
   */
  describe("enqueueDocument", () => {
    it("should create a job record and return job ID", async () => {
      // Setup mock to return a job ID
      mockEnqueueDocument.mockResolvedValueOnce("job-123");

      const documentId = "doc-456";
      const processingType = "analysis";

      const result = await queueService.enqueueDocument(
        documentId,
        processingType,
        tenantId,
      );

      expect(result).toBe("job-123");
      expect(mockEnqueueDocument).toHaveBeenCalledWith(
        documentId,
        processingType,
        tenantId,
      );
    });

    it("should handle enqueue errors gracefully", async () => {
      mockEnqueueDocument.mockRejectedValueOnce(
        new Error("Failed to enqueue document"),
      );

      const documentId = "doc-456";
      const processingType = "analysis";

      await expect(
        queueService.enqueueDocument(documentId, processingType, tenantId),
      ).rejects.toThrow("Failed to enqueue document");
    });

    it("should validate required parameters", async () => {
      mockEnqueueDocument.mockRejectedValueOnce(
        new Error("Document ID is required"),
      );

      await expect(
        queueService.enqueueDocument(null, "analysis", tenantId),
      ).rejects.toThrow("Document ID is required");
    });
  });

  /**
   * Tests for the getJobStatus method which retrieves the current status of a processing job
   */
  describe("getJobStatus", () => {
    it("should return job status for valid job ID", async () => {
      const mockJobStatus = {
        id: "job-123",
        document_id: "doc-456",
        status: "processing",
        progress: 50,
        created_at: "2024-01-01T10:00:00Z",
        updated_at: "2024-01-01T10:30:00Z",
        tenant_id: tenantId,
      };

      mockGetJobStatus.mockResolvedValueOnce(mockJobStatus);

      const result = await queueService.getJobStatus("job-123", tenantId);

      expect(result).toEqual(mockJobStatus);
      expect(mockGetJobStatus).toHaveBeenCalledWith("job-123", tenantId);
    });

    it("should return null for non-existent job", async () => {
      mockGetJobStatus.mockResolvedValueOnce(null);

      const result = await queueService.getJobStatus(
        "non-existent-job",
        tenantId,
      );

      expect(result).toBeNull();
      expect(mockGetJobStatus).toHaveBeenCalledWith(
        "non-existent-job",
        tenantId,
      );
    });

    it("should handle database errors", async () => {
      mockGetJobStatus.mockRejectedValueOnce(new Error("Database error"));

      await expect(
        queueService.getJobStatus("job-123", tenantId),
      ).rejects.toThrow("Database error");
    });
  });

  /**
   * Tests for the getDocumentJobs method which retrieves all jobs for a specific document
   */
  describe("getDocumentJobs", () => {
    it("should return all jobs for a document", async () => {
      const mockJobs = [
        {
          id: "job-1",
          document_id: "doc-456",
          status: "completed",
          processing_type: "analysis",
          created_at: "2024-01-01T10:00:00Z",
          tenant_id: tenantId,
        },
        {
          id: "job-2",
          document_id: "doc-456",
          status: "processing",
          processing_type: "extraction",
          created_at: "2024-01-01T11:00:00Z",
          tenant_id: tenantId,
        },
      ];

      mockGetDocumentJobs.mockResolvedValueOnce(mockJobs);

      const result = await queueService.getDocumentJobs("doc-456", tenantId);

      expect(result).toEqual(mockJobs);
      expect(result).toHaveLength(2);
      expect(mockGetDocumentJobs).toHaveBeenCalledWith("doc-456", tenantId);
    });

    it("should return empty array for document with no jobs", async () => {
      mockGetDocumentJobs.mockResolvedValueOnce([]);

      const result = await queueService.getDocumentJobs("doc-789", tenantId);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it("should handle errors when retrieving document jobs", async () => {
      mockGetDocumentJobs.mockRejectedValueOnce(
        new Error("Failed to retrieve jobs"),
      );

      await expect(
        queueService.getDocumentJobs("doc-456", tenantId),
      ).rejects.toThrow("Failed to retrieve jobs");
    });
  });

  /**
   * Tests for the reprocessDocument method which requeues a document for processing
   */
  describe("reprocessDocument", () => {
    it("should successfully reprocess a document", async () => {
      const newJobId = "job-reprocess-123";
      mockReprocessDocument.mockResolvedValueOnce(newJobId);

      const documentId = "doc-456";
      const processingType = "analysis";

      const result = await queueService.reprocessDocument(
        documentId,
        processingType,
        tenantId,
      );

      expect(result).toBe(newJobId);
      expect(mockReprocessDocument).toHaveBeenCalledWith(
        documentId,
        processingType,
        tenantId,
      );
    });

    it("should handle reprocessing errors", async () => {
      mockReprocessDocument.mockRejectedValueOnce(
        new Error("Failed to reprocess document"),
      );

      const documentId = "doc-456";
      const processingType = "analysis";

      await expect(
        queueService.reprocessDocument(documentId, processingType, tenantId),
      ).rejects.toThrow("Failed to reprocess document");
    });

    it("should validate document exists before reprocessing", async () => {
      mockReprocessDocument.mockRejectedValueOnce(
        new Error("Document not found"),
      );

      await expect(
        queueService.reprocessDocument("non-existent", "analysis", tenantId),
      ).rejects.toThrow("Document not found");
    });
  });

  /**
   * Integration tests that verify the service works correctly with different scenarios
   */
  describe("Integration scenarios", () => {
    it("should handle complete document processing workflow", async () => {
      const documentId = "doc-workflow";
      const processingType = "full-analysis";

      // Step 1: Enqueue document
      mockEnqueueDocument.mockResolvedValueOnce("job-workflow-1");
      const jobId = await queueService.enqueueDocument(
        documentId,
        processingType,
        tenantId,
      );

      expect(jobId).toBe("job-workflow-1");

      // Step 2: Check job status
      mockGetJobStatus.mockResolvedValueOnce({
        id: jobId,
        document_id: documentId,
        status: "completed",
        progress: 100,
        tenant_id: tenantId,
      });

      const status = await queueService.getJobStatus(jobId, tenantId);
      expect(status.status).toBe("completed");

      // Step 3: Get all jobs for document
      mockGetDocumentJobs.mockResolvedValueOnce([
        {
          id: jobId,
          document_id: documentId,
          status: "completed",
          processing_type: processingType,
          tenant_id: tenantId,
        },
      ]);

      const allJobs = await queueService.getDocumentJobs(documentId, tenantId);
      expect(allJobs).toHaveLength(1);
      expect(allJobs[0].id).toBe(jobId);
    });
  });
});
