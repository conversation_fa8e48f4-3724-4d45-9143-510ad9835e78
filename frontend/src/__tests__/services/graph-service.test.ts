/**
 * Graph Service Tests
 * 
 * Tests for Neo4j graph database operations including person networks,
 * case relationships, and conflict checking functionality.
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import { GraphService } from "@/lib/services/graph-service";
import { 
  makeNeo4jMock, 
  create<PERSON>ersonNode, 
  createCaseNode, 
  createRelationship,
  createMockRecord,
  createMockResult,
  configureMockSessionForGraph,
  type MockNeo4jDriver,
  type MockNeo4jSession
} from "@/test/helpers/neo4j-mock";

// Mock the neo4j-driver library
vi.mock("neo4j-driver", () => ({
  driver: vi.fn(),
  auth: {
    basic: vi.fn(),
  },
  isInt: (val: any) => typeof val === "number" || typeof val === "bigint",
  integer: {
    fromNumber: (num: number) => BigInt(num),
    toNumber: (int: any) => Number(int),
  },
  isPoint: vi.fn().mockReturnValue(false),
}));

// Mock the GraphService to use our mock driver
vi.mock("@/lib/services/graph-service", async () => {
  const actual = await vi.importActual<any>("@/lib/services/graph-service");
  
  class TestGraphService extends actual.GraphService {
    private mockDriver: MockNeo4jDriver;
    private mockSession: MockNeo4jSession;

    constructor() {
      super();
      this.mockDriver = makeNeo4jMock();
      this.mockSession = this.mockDriver.session();
    }

    // Override the private getSession method to return our mock session
    getSession() {
      return this.mockSession as any;
    }

    // Expose mock for test configuration
    getMockSession() {
      return this.mockSession;
    }
  }

  return {
    ...actual,
    GraphService: TestGraphService,
  };
});

describe("GraphService", () => {
  let graphService: any;
  let mockSession: MockNeo4jSession;

  beforeEach(() => {
    vi.clearAllMocks();
    graphService = new GraphService();
    mockSession = graphService.getMockSession();
  });

  describe("getPersonNetwork", () => {
    it("should return nodes and links when person has connections", async () => {
      const personId = "person-123";
      
      // Create mock data
      const personNode = createPersonNode("1", "John Doe", { id: personId });
      const relatedNode = createPersonNode("2", "Jane Smith", { id: "person-456" });
      const relationship = createRelationship("1", "KNOWS", "1", "2", { since: "2020" });

      // Configure mock session
      const mockRecords = [
        createMockRecord({ 
          person: personNode,
          related: relatedNode,
          relationship: relationship
        })
      ];
      
      mockSession.run.mockResolvedValue(createMockResult(mockRecords));

      const result = await graphService.getPersonNetwork(personId);

      expect(result).toBeDefined();
      expect(result.nodes).toHaveLength(2);
      expect(result.links).toHaveLength(1);
      expect(result.nodes[0].id).toBe(personId);
      expect(result.nodes[1].id).toBe("person-456");
      expect(result.links[0].source).toBe(personId);
      expect(result.links[0].target).toBe("person-456");
    });

    it("should return only the person node when they have no connections", async () => {
      const personId = "person-lonely";
      
      const personNode = createPersonNode("1", "Lonely Person", { id: personId });
      const mockRecords = [createMockRecord({ person: personNode })];
      
      mockSession.run.mockResolvedValue(createMockResult(mockRecords));

      const result = await graphService.getPersonNetwork(personId);

      expect(result.nodes).toHaveLength(1);
      expect(result.links).toHaveLength(0);
      expect(result.nodes[0].id).toBe(personId);
    });

    it("should return an empty graph when the person is not found", async () => {
      const personId = "person-nonexistent";
      
      mockSession.run.mockResolvedValue(createMockResult([]));

      const result = await graphService.getPersonNetwork(personId);

      expect(result.nodes).toHaveLength(0);
      expect(result.links).toHaveLength(0);
    });

    it("should throw an error if the database connection fails", async () => {
      const personId = "person-123";
      
      mockSession.run.mockRejectedValue(new Error("Database connection failed"));

      await expect(graphService.getPersonNetwork(personId)).rejects.toThrow(
        "Database connection failed"
      );
    });
  });

  describe("getStaffCases", () => {
    it("should return cases handled by the staff member", async () => {
      const staffId = "staff-123";
      
      const caseNode1 = createCaseNode("1", "Case Alpha", { id: "case-1" });
      const caseNode2 = createCaseNode("2", "Case Beta", { id: "case-2" });
      
      const mockRecords = [
        createMockRecord({ case: caseNode1 }),
        createMockRecord({ case: caseNode2 })
      ];
      
      mockSession.run.mockResolvedValue(createMockResult(mockRecords));

      const result = await graphService.getStaffCases(staffId);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe("case-1");
      expect(result[0].title).toBe("Case Alpha");
      expect(result[1].id).toBe("case-2");
      expect(result[1].title).toBe("Case Beta");
    });

    it("should return an empty array when staff has no cases", async () => {
      const staffId = "staff-nocases";
      
      mockSession.run.mockResolvedValue(createMockResult([]));

      const result = await graphService.getStaffCases(staffId);

      expect(result).toHaveLength(0);
    });

    it("should handle database errors gracefully", async () => {
      const staffId = "staff-123";
      
      mockSession.run.mockRejectedValue(new Error("Query failed"));

      await expect(graphService.getStaffCases(staffId)).rejects.toThrow(
        "Query failed"
      );
    });
  });

  describe("getCaseNetwork", () => {
    it("should return nodes and links for a case network", async () => {
      const caseId = "case-123";
      
      const caseNode = createCaseNode("1", "Main Case", { id: caseId });
      const personNode = createPersonNode("2", "Client Name", { id: "person-1" });
      const relationship = createRelationship("1", "CLIENT_OF", "2", "1");

      const mockRecords = [
        createMockRecord({
          case: caseNode,
          person: personNode,
          relationship: relationship
        })
      ];
      
      mockSession.run.mockResolvedValue(createMockResult(mockRecords));

      const result = await graphService.getCaseNetwork(caseId);

      expect(result.nodes).toHaveLength(2);
      expect(result.links).toHaveLength(1);
      expect(result.nodes.find(n => n.type === "case")).toBeDefined();
      expect(result.nodes.find(n => n.type === "person")).toBeDefined();
    });

    it("should return just the case node when there are no connections", async () => {
      const caseId = "case-isolated";
      
      const caseNode = createCaseNode("1", "Isolated Case", { id: caseId });
      const mockRecords = [createMockRecord({ case: caseNode })];
      
      mockSession.run.mockResolvedValue(createMockResult(mockRecords));

      const result = await graphService.getCaseNetwork(caseId);

      expect(result.nodes).toHaveLength(1);
      expect(result.links).toHaveLength(0);
      expect(result.nodes[0].id).toBe(caseId);
    });

    it("should return empty network for non-existent case", async () => {
      const caseId = "case-nonexistent";
      
      mockSession.run.mockResolvedValue(createMockResult([]));

      const result = await graphService.getCaseNetwork(caseId);

      expect(result.nodes).toHaveLength(0);
      expect(result.links).toHaveLength(0);
    });
  });

  describe("checkConflicts", () => {
    it("should identify conflicts with existing clients", async () => {
      const newClientId = "client-new";
      const existingCaseId = "case-existing";
      
      const conflictNode = createPersonNode("1", "Conflicted Person", { 
        id: "person-conflict" 
      });
      const caseNode = createCaseNode("2", "Existing Case", { 
        id: existingCaseId 
      });
      
      const mockRecords = [
        createMockRecord({
          conflictPerson: conflictNode,
          conflictCase: caseNode,
          conflictType: "OPPOSING_PARTY"
        })
      ];
      
      mockSession.run.mockResolvedValue(createMockResult(mockRecords));

      const result = await graphService.checkConflicts(newClientId);

      expect(result).toHaveLength(1);
      expect(result[0].conflictType).toBe("OPPOSING_PARTY");
      expect(result[0].conflictCase.id).toBe(existingCaseId);
    });

    it("should return empty array when no conflicts found", async () => {
      const newClientId = "client-clean";
      
      mockSession.run.mockResolvedValue(createMockResult([]));

      const result = await graphService.checkConflicts(newClientId);

      expect(result).toHaveLength(0);
    });

    it("should handle database errors gracefully", async () => {
      const newClientId = "client-error";
      
      mockSession.run.mockRejectedValue(new Error("Conflict check failed"));

      await expect(graphService.checkConflicts(newClientId)).rejects.toThrow(
        "Conflict check failed"
      );
    });
  });

  describe("Database connection management", () => {
    it("should properly close sessions after operations", async () => {
      const personId = "person-123";
      
      mockSession.run.mockResolvedValue(createMockResult([]));

      await graphService.getPersonNetwork(personId);

      expect(mockSession.close).toHaveBeenCalled();
    });

    it("should handle session errors gracefully", async () => {
      const personId = "person-123";
      
      mockSession.close.mockRejectedValue(new Error("Session close failed"));
      mockSession.run.mockResolvedValue(createMockResult([]));

      // Should not throw even if session close fails
      await expect(graphService.getPersonNetwork(personId)).resolves.toBeDefined();
    });
  });
});
