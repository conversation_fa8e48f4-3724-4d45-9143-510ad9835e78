import { describe, it, expect, vi, beforeEach } from "vitest";
import { UsageTrackingService } from "@/lib/services/usage-tracking-service";

// Mock Supabase client
const mockSupabase = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  gte: vi.fn().mockReturnThis(),
  lte: vi.fn().mockReturnThis(),
  order: vi.fn().mockReturnThis(),
  limit: vi.fn().mockReturnThis(),
  single: vi.fn(),
  insert: vi.fn().mockReturnThis(),
  update: vi.fn().mockReturnThis(),
  upsert: vi.fn().mockReturnThis(),
  delete: vi.fn().mockReturnThis(),
  rpc: vi.fn(),
};

describe("UsageTrackingService", () => {
  let usageTrackingService: UsageTrackingService;

  beforeEach(() => {
    vi.clearAllMocks();
    usageTrackingService = new UsageTrackingService(mockSupabase as any);
  });

  describe("trackResourceUsage", () => {
    it("should track resource usage", async () => {
      const mockUsage = {
        id: "1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 1,
        resource_size_bytes: 1024,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      mockSupabase.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: mockUsage, error: null }),
          }),
        }),
      });

      const result = await usageTrackingService.trackResourceUsage({
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 1,
        resourceSizeBytes: 1024,
      });

      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        id: "1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 1,
        resourceSizeBytes: 1024,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });
    });
  });

  describe("incrementResourceUsage", () => {
    it("should increment resource usage for the current period", async () => {
      // Mock getting current period usage
      const mockCurrentUsage = {
        id: "1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 5,
        resource_size_bytes: 5120,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      mockSupabase.from.mockReturnValueOnce({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  single: vi
                    .fn()
                    .mockResolvedValue({ data: mockCurrentUsage, error: null }),
                }),
              }),
            }),
          }),
        }),
      });

      // Mock updating usage
      const mockUpdatedUsage = {
        ...mockCurrentUsage,
        usage_count: 6,
        resource_size_bytes: 6144,
      };

      mockSupabase.from.mockReturnValueOnce({
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi
                .fn()
                .mockResolvedValue({ data: mockUpdatedUsage, error: null }),
            }),
          }),
        }),
      });

      const result = await usageTrackingService.incrementResourceUsage({
        tenantId: "tenant-1",
        usageType: "document_upload",
        incrementBy: 1,
        resourceSizeBytes: 1024,
      });

      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        id: "1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 6,
        resourceSizeBytes: 6144,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });
    });

    it("should create new usage record if none exists for the current period", async () => {
      // Mock getting current period usage (not found)
      mockSupabase.from.mockReturnValueOnce({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  single: vi
                    .fn()
                    .mockResolvedValue({ data: null, error: null }),
                }),
              }),
            }),
          }),
        }),
      });

      // Mock creating new usage
      const mockNewUsage = {
        id: "1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 1,
        resource_size_bytes: 1024,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      mockSupabase.from.mockReturnValueOnce({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi
              .fn()
              .mockResolvedValue({ data: mockNewUsage, error: null }),
          }),
        }),
      });

      const result = await usageTrackingService.incrementResourceUsage({
        tenantId: "tenant-1",
        usageType: "document_upload",
        incrementBy: 1,
        resourceSizeBytes: 1024,
      });

      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        id: "1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 1,
        resourceSizeBytes: 1024,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });
    });
  });

  describe("getTenantUsage", () => {
    it("should get tenant usage for a specific period and type", async () => {
      const mockUsage = [
        {
          id: "1",
          tenant_id: "tenant-1",
          usage_type: "document_upload",
          usage_count: 10,
          resource_size_bytes: 10240,
          period_start: "2023-01-01T00:00:00Z",
          period_end: "2023-01-31T23:59:59Z",
          created_at: "2023-01-15T12:00:00Z",
        },
        {
          id: "2",
          tenant_id: "tenant-1",
          usage_type: "document_upload",
          usage_count: 15,
          resource_size_bytes: 15360,
          period_start: "2023-02-01T00:00:00Z",
          period_end: "2023-02-28T23:59:59Z",
          created_at: "2023-02-15T12:00:00Z",
        },
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  order: vi
                    .fn()
                    .mockResolvedValue({ data: mockUsage, error: null }),
                }),
              }),
            }),
          }),
        }),
      });

      const result = await usageTrackingService.getTenantUsage({
        tenantId: "tenant-1",
        usageType: "document_upload",
        startDate: new Date("2023-01-01"),
        endDate: new Date("2023-02-28"),
      });

      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual([
        {
          id: "1",
          tenantId: "tenant-1",
          usageType: "document_upload",
          usageCount: 10,
          resourceSizeBytes: 10240,
          periodStart: "2023-01-01T00:00:00Z",
          periodEnd: "2023-01-31T23:59:59Z",
          createdAt: "2023-01-15T12:00:00Z",
        },
        {
          id: "2",
          tenantId: "tenant-1",
          usageType: "document_upload",
          usageCount: 15,
          resourceSizeBytes: 15360,
          periodStart: "2023-02-01T00:00:00Z",
          periodEnd: "2023-02-28T23:59:59Z",
          createdAt: "2023-02-15T12:00:00Z",
        },
      ]);
    });
  });

  describe("getCurrentPeriodUsage", () => {
    it("should get current period usage for a specific type", async () => {
      const mockUsage = {
        id: "1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 10,
        resource_size_bytes: 10240,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  single: vi
                    .fn()
                    .mockResolvedValue({ data: mockUsage, error: null }),
                }),
              }),
            }),
          }),
        }),
      });

      const result = await usageTrackingService.getCurrentPeriodUsage(
        "tenant-1",
        "document_upload",
      );

      expect(mockSupabase.from).toHaveBeenCalledWith("resource_usage");
      expect(result).toEqual({
        id: "1",
        tenantId: "tenant-1",
        usageType: "document_upload",
        usageCount: 10,
        resourceSizeBytes: 10240,
        periodStart: "2023-01-01T00:00:00Z",
        periodEnd: "2023-01-31T23:59:59Z",
        createdAt: "2023-01-15T12:00:00Z",
      });
    });

    it("should return null if no usage exists for the current period", async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  single: vi
                    .fn()
                    .mockResolvedValue({ data: null, error: null }),
                }),
              }),
            }),
          }),
        }),
      });

      const result = await usageTrackingService.getCurrentPeriodUsage(
        "tenant-1",
        "document_upload",
      );

      expect(result).toBeNull();
    });
  });

  describe("checkQuotaLimit", () => {
    it("should check if usage exceeds quota limit", async () => {
      // Mock getting tenant quota
      const mockQuota = {
        tenant_id: "tenant-1",
        max_daily_uploads: 10,
        max_monthly_uploads: 100,
        max_document_size_mb: 10,
        max_concurrent_processing: 5,
        plan_tier: "basic",
      };

      mockSupabase.from.mockReturnValueOnce({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: mockQuota, error: null }),
          }),
        }),
      });

      // Mock getting current period usage
      const mockUsage = {
        id: "1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 5,
        resource_size_bytes: 5120,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      mockSupabase.from.mockReturnValueOnce({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  single: vi
                    .fn()
                    .mockResolvedValue({ data: mockUsage, error: null }),
                }),
              }),
            }),
          }),
        }),
      });

      const result = await usageTrackingService.checkQuotaLimit({
        tenantId: "tenant-1",
        usageType: "document_upload",
        incrementBy: 1,
      });

      expect(result).toEqual({
        withinQuota: true,
        currentUsage: 5,
        quotaLimit: 100,
        percentUsed: 5,
      });
    });

    it("should return false if usage exceeds quota limit", async () => {
      // Mock getting tenant quota
      const mockQuota = {
        tenant_id: "tenant-1",
        max_daily_uploads: 10,
        max_monthly_uploads: 100,
        max_document_size_mb: 10,
        max_concurrent_processing: 5,
        plan_tier: "basic",
      };

      mockSupabase.from.mockReturnValueOnce({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({ data: mockQuota, error: null }),
          }),
        }),
      });

      // Mock getting current period usage
      const mockUsage = {
        id: "1",
        tenant_id: "tenant-1",
        usage_type: "document_upload",
        usage_count: 99,
        resource_size_bytes: 101376,
        period_start: "2023-01-01T00:00:00Z",
        period_end: "2023-01-31T23:59:59Z",
        created_at: "2023-01-15T12:00:00Z",
      };

      mockSupabase.from.mockReturnValueOnce({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              gte: vi.fn().mockReturnValue({
                lte: vi.fn().mockReturnValue({
                  single: vi
                    .fn()
                    .mockResolvedValue({ data: mockUsage, error: null }),
                }),
              }),
            }),
          }),
        }),
      });

      const result = await usageTrackingService.checkQuotaLimit({
        tenantId: "tenant-1",
        usageType: "document_upload",
        incrementBy: 2,
      });

      expect(result).toEqual({
        withinQuota: false,
        currentUsage: 99,
        quotaLimit: 100,
        percentUsed: 99,
      });
    });
  });
});
