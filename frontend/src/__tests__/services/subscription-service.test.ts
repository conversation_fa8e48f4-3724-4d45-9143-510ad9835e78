import { describe, it, expect, beforeEach, vi } from "vitest";
import { SubscriptionService } from "@/lib/services/subscription-service";

// Create a comprehensive mock that supports complex chaining
const createMockChain = () => {
  const mockChain = {
    from: vi.fn(),
    select: vi.fn(),
    eq: vi.fn(),
    order: vi.fn(),
    limit: vi.fn(),
    single: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    rpc: vi.fn(),
    schema: vi.fn(),
  };

  // Make all methods return the chain for fluent interface
  Object.keys(mockChain).forEach((key) => {
    if (key !== "single" && key !== "rpc") {
      mockChain[key].mockReturnValue(mockChain);
    }
  });

  return mockChain;
};

const mockSupabase = createMockChain();

describe("SubscriptionService", () => {
  let subscriptionService: SubscriptionService;

  beforeEach(() => {
    vi.clearAllMocks();

    // Re-setup the mock chain after reset
    Object.keys(mockSupabase).forEach((key) => {
      if (key !== "single" && key !== "rpc") {
        mockSupabase[key].mockReturnValue(mockSupabase);
      }
    });

    subscriptionService = new SubscriptionService(mockSupabase as any);
  });

  describe("getSubscriptionPlans", () => {
    it("should return subscription plans", async () => {
      const mockPlans = [
        {
          id: "1",
          name: "Basic Plan",
          code: "basic",
          description: "Basic plan for small firms",
          is_active: true,
          is_public: true,
          base_price_monthly: 99,
          base_price_yearly: 999,
          features: { hasIntakeAgent: true },
          created_at: "2023-01-01T00:00:00Z",
          updated_at: "2023-01-01T00:00:00Z",
        },
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({ data: mockPlans, error: null }),
          }),
        }),
      });

      const result = await subscriptionService.getSubscriptionPlans();

      expect(mockSupabase.from).toHaveBeenCalledWith("subscription_plans");
      expect(result).toEqual([
        {
          id: "1",
          name: "Basic Plan",
          code: "basic",
          description: "Basic plan for small firms",
          isActive: true,
          isPublic: true,
          basePriceMonthly: 99,
          basePriceYearly: 999,
          features: { hasIntakeAgent: true },
          createdAt: "2023-01-01T00:00:00Z",
          updatedAt: "2023-01-01T00:00:00Z",
        },
      ]);
    });

    it("should throw an error if the query fails", async () => {
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: null,
              error: new Error("Query failed"),
            }),
          }),
        }),
      });

      await expect(subscriptionService.getSubscriptionPlans()).rejects.toThrow(
        "Query failed",
      );
    });
  });

  describe("getSubscriptionAddons", () => {
    it("should return subscription addons", async () => {
      const mockAddons = [
        {
          id: "1",
          name: "Extra Users",
          code: "extra_users",
          description: "Add more users to your plan",
          category: "users",
          is_active: true,
          price_monthly: 19,
          price_yearly: 190,
          features: { maxUsers: 5 },
          created_at: "2023-01-01T00:00:00Z",
          updated_at: "2023-01-01T00:00:00Z",
        },
      ];

      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({ data: mockAddons, error: null }),
          }),
        }),
      });

      const result = await subscriptionService.getSubscriptionAddons();

      expect(mockSupabase.from).toHaveBeenCalledWith("subscription_addons");
      expect(result).toEqual([
        {
          id: "1",
          name: "Extra Users",
          code: "extra_users",
          description: "Add more users to your plan",
          category: "users",
          isActive: true,
          priceMonthly: 19,
          priceYearly: 190,
          features: { maxUsers: 5 },
          createdAt: "2023-01-01T00:00:00Z",
          updatedAt: "2023-01-01T00:00:00Z",
        },
      ]);
    });
  });

  describe("getTenantSubscription", () => {
    it("should return tenant subscription", async () => {
      const mockSubscription = {
        id: "1",
        tenant_id: "tenant-1",
        plan_id: "plan-1",
        status: "active",
        billing_cycle: "monthly",
        trial_start: "2023-01-01T00:00:00Z",
        trial_end: "2023-01-15T00:00:00Z",
        current_period_start: "2023-01-01T00:00:00Z",
        current_period_end: "2023-02-01T00:00:00Z",
        canceled_at: null,
        payment_provider: "stripe",
        payment_provider_subscription_id: "sub_123",
        metadata: { notes: "Test subscription" },
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
        // This should be subscription_plans, not plan, to match the database query
        subscription_plans: {
          name: "Basic Plan",
          code: "basic",
          base_price_monthly: 99,
          base_price_yearly: 999,
          features: { hasIntakeAgent: true },
        },
      };

      // Mock the getTenantSubscription call (uses .single())
      mockSupabase.single.mockResolvedValueOnce({
        data: mockSubscription,
        error: null,
      });

      // Mock the getTenantAddons call (doesn't use .single(), returns data directly)
      // This needs to mock the entire chain: schema().from().select().eq().eq().eq()
      const mockAddonsResponse = { data: [], error: null };

      // Create a mock that handles the complex chaining for getTenantAddons
      const mockAddonsChain = {
        eq: vi.fn().mockReturnThis(),
      };
      mockAddonsChain.eq.mockReturnValue(mockAddonsChain);

      // Override the from method to return different chains based on the table
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === "tenant_subscriptions") {
          // For getTenantSubscription - return the original chain
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                order: vi.fn().mockReturnValue({
                  limit: vi.fn().mockReturnValue({
                    single: vi.fn().mockResolvedValue({
                      data: mockSubscription,
                      error: null,
                    }),
                  }),
                }),
              }),
            }),
          };
        } else if (table === "tenant_addons") {
          // For getTenantAddons - return a chain that resolves to the addons response
          return {
            select: vi.fn().mockReturnValue({
              eq: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                  eq: vi.fn().mockResolvedValue(mockAddonsResponse),
                }),
              }),
            }),
          };
        }
        return mockSupabase;
      });

      const result =
        await subscriptionService.getTenantSubscription("tenant-1");

      expect(mockSupabase.from).toHaveBeenCalledWith("tenant_subscriptions");
      expect(result).toEqual({
        id: "1",
        tenantId: "tenant-1",
        planId: "plan-1",
        status: "active",
        billingCycle: "monthly",
        trialStart: "2023-01-01T00:00:00Z",
        trialEnd: "2023-01-15T00:00:00Z",
        currentPeriodStart: "2023-01-01T00:00:00Z",
        currentPeriodEnd: "2023-02-01T00:00:00Z",
        canceledAt: null,
        paymentProvider: "stripe",
        paymentProviderSubscriptionId: "sub_123",
        metadata: { notes: "Test subscription" },
        createdAt: "2023-01-01T00:00:00Z",
        updatedAt: "2023-01-01T00:00:00Z",
        plan: {
          name: "Basic Plan",
          code: "basic",
          basePriceMonthly: 99,
          basePriceYearly: 999,
          features: { hasIntakeAgent: true },
        },
        addons: [], // Empty addons array to match our mock
      });
    });
  });

  describe("createSubscription", () => {
    it("should create a subscription", async () => {
      const mockSubscription = {
        id: "1",
        tenant_id: "tenant-1",
        plan_id: "plan-1",
        status: "active",
        billing_cycle: "monthly",
      };

      mockSupabase.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi
              .fn()
              .mockResolvedValue({ data: mockSubscription, error: null }),
          }),
        }),
      });

      const result = await subscriptionService.createSubscription({
        tenantId: "tenant-1",
        planId: "plan-1",
        billingCycle: "monthly",
      });

      expect(mockSupabase.from).toHaveBeenCalledWith("tenant_subscriptions");
      expect(result).toEqual({
        id: "1",
        tenantId: "tenant-1",
        planId: "plan-1",
        status: "active",
        billingCycle: "monthly",
      });
    });
  });

  describe("updateSubscription", () => {
    it("should update a subscription", async () => {
      const mockSubscription = {
        id: "1",
        tenant_id: "tenant-1",
        plan_id: "plan-2",
        status: "active",
        billing_cycle: "yearly",
      };

      mockSupabase.from.mockReturnValue({
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi
                .fn()
                .mockResolvedValue({ data: mockSubscription, error: null }),
            }),
          }),
        }),
      });

      const result = await subscriptionService.updateSubscription("1", {
        planId: "plan-2",
        billingCycle: "yearly",
      });

      expect(mockSupabase.from).toHaveBeenCalledWith("tenant_subscriptions");
      expect(result).toEqual({
        id: "1",
        tenantId: "tenant-1",
        planId: "plan-2",
        status: "active",
        billingCycle: "yearly",
      });
    });
  });

  describe("cancelSubscription", () => {
    it("should cancel a subscription", async () => {
      mockSupabase.from.mockReturnValue({
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: { id: "1", status: "canceled" },
                error: null,
              }),
            }),
          }),
        }),
      });

      const result = await subscriptionService.cancelSubscription("1");

      expect(mockSupabase.from).toHaveBeenCalledWith("tenant_subscriptions");
      expect(result).toEqual({ id: "1", status: "canceled" });
    });
  });

  describe("addAddon", () => {
    it("should add an addon to a subscription", async () => {
      const mockSubscription = {
        id: "1",
        tenant_id: "tenant-1",
        plan_id: "plan-1",
        status: "active",
      };

      const mockAddon = {
        id: "addon-1",
        tenant_id: "tenant-1",
        subscription_id: "1",
        addon_id: "addon-type-1",
        status: "active",
        quantity: 2,
      };

      // Mock the getSubscriptionById call (first call in addAddon)
      mockSupabase.single.mockResolvedValueOnce({
        data: mockSubscription,
        error: null,
      });

      // Mock the addAddon insert call (second call in addAddon)
      mockSupabase.single.mockResolvedValueOnce({
        data: mockAddon,
        error: null,
      });

      const result = await subscriptionService.addAddon({
        tenantId: "tenant-1",
        subscriptionId: "1",
        addonId: "addon-type-1",
        quantity: 2,
      });

      expect(result).toEqual({
        id: "addon-1",
        tenantId: "tenant-1",
        subscriptionId: "1",
        addonId: "addon-type-1",
        status: "active",
        quantity: 2,
      });
    });
  });

  describe("cancelAddon", () => {
    it("should cancel an addon from a subscription", async () => {
      mockSupabase.from.mockReturnValue({
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: { id: "addon-1", status: "canceled" },
                error: null,
              }),
            }),
          }),
        }),
      });

      const result = await subscriptionService.cancelAddon("addon-1");

      expect(mockSupabase.from).toHaveBeenCalledWith("tenant_addons");
      expect(result).toEqual({ id: "addon-1", status: "canceled" });
    });
  });

  describe("checkFeatureAccess", () => {
    it("should check if a tenant has access to a feature", async () => {
      mockSupabase.rpc.mockResolvedValue({ data: true, error: null });

      const result = await subscriptionService.checkFeatureAccess(
        "tenant-1",
        "hasIntakeAgent",
      );

      expect(mockSupabase.rpc).toHaveBeenCalledWith("check_feature_access", {
        p_tenant_id: "tenant-1",
        p_feature_key: "hasIntakeAgent",
      });
      expect(result).toBe(true);
    });

    it("should throw an error if the query fails", async () => {
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: new Error("Query failed"),
      });

      await expect(
        subscriptionService.checkFeatureAccess("tenant-1", "hasIntakeAgent"),
      ).rejects.toThrow("Query failed");
    });
  });
});
