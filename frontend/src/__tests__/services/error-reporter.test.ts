/**
 * Error Reporter Tests
 *
 * Tests for the error reporting system including error classification,
 * Supabase integration, and error handling patterns.
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import { ErrorReporter } from "@/lib/services/error-reporter";
import { ErrorType } from "@/lib/error-handling/error-types";
import { AGError } from "@/lib/error-handling/error-classes";

// Mock Supabase client
const mockSupabase = {
  rpc: vi.fn().mockReturnValue({
    _data: "mocked-error-id",
    error: null,
  }),
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  insert: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
};

vi.mock("@/lib/supabase/client", () => ({
  supabase: mockSupabase,
}));

// Mock console methods
const mockConsoleError = vi.fn();
const mockConsoleWarn = vi.fn();
const mockConsoleLog = vi.fn();

describe("ErrorReporter", () => {
  let errorReporter: ErrorReporter;
  let originalConsoleError: any;
  let originalConsoleWarn: any;
  let originalConsoleLog: any;

  beforeEach(() => {
    // Save original console methods
    originalConsoleError = console.error;
    originalConsoleWarn = console.warn;
    originalConsoleLog = console.log;

    // Mock console methods
    console.error = mockConsoleError;
    console.warn = mockConsoleWarn;
    console.log = mockConsoleLog;

    // Clear all mocks
    vi.clearAllMocks();

    // Reset the singleton instance for each test
    // @ts-expect-error - accessing private property for testing
    ErrorReporter.instance = undefined;

    errorReporter = ErrorReporter.getInstance();
  });

  afterEach(() => {
    // Restore original console methods
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
    console.log = originalConsoleLog;
  });

  describe("Singleton pattern", () => {
    it("should return the same instance when called multiple times", () => {
      const instance1 = ErrorReporter.getInstance();
      const instance2 = ErrorReporter.getInstance();

      expect(instance1).toBe(instance2);
      expect(instance1).toBe(errorReporter);
    });
  });

  describe("Error reporting", () => {
    it("should report a basic error", async () => {
      const error = new Error("Test error");
      const context = { userId: "user-123", action: "test-action" };

      mockSupabase.rpc.mockResolvedValue({
        data: "error-id-123",
        error: null,
      });

      const result = await errorReporter.reportError(error, context);

      expect(result).toBe("error-id-123");
      expect(mockSupabase.rpc).toHaveBeenCalledWith("log_error", {
        error_type: "Error",
        error_message: "Test error",
        error_stack: error.stack,
        context: JSON.stringify(context),
        user_id: "user-123",
      });
      expect(mockConsoleError).toHaveBeenCalledWith("Error reported:", error, context);
    });

    it("should report an AGError with proper classification", async () => {
      const agError = new AGError(
        "Authentication failed",
        ErrorType.AUTHENTICATION_ERROR,
        { userId: "user-123" }
      );

      mockSupabase.rpc.mockResolvedValue({
        data: "error-id-456",
        error: null,
      });

      const result = await errorReporter.reportError(agError);

      expect(result).toBe("error-id-456");
      expect(mockSupabase.rpc).toHaveBeenCalledWith("log_error", {
        error_type: ErrorType.AUTHENTICATION_ERROR,
        error_message: "Authentication failed",
        error_stack: agError.stack,
        context: JSON.stringify({ userId: "user-123" }),
        user_id: null,
      });
    });

    it("should handle Supabase errors gracefully", async () => {
      const error = new Error("Test error");
      
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: "Database connection failed" },
      });

      const result = await errorReporter.reportError(error);

      expect(result).toBeNull();
      expect(mockConsoleError).toHaveBeenCalledWith(
        "Failed to report error to Supabase:",
        { message: "Database connection failed" }
      );
    });

    it("should handle network errors when reporting", async () => {
      const error = new Error("Test error");
      
      mockSupabase.rpc.mockRejectedValue(new Error("Network error"));

      const result = await errorReporter.reportError(error);

      expect(result).toBeNull();
      expect(mockConsoleError).toHaveBeenCalledWith(
        "Exception while reporting error:",
        expect.any(Error)
      );
    });
  });

  describe("Error classification", () => {
    it("should classify different error types correctly", async () => {
      const testCases = [
        {
          error: new AGError("Auth failed", ErrorType.AUTHENTICATION_ERROR),
          expectedType: ErrorType.AUTHENTICATION_ERROR,
        },
        {
          error: new AGError("Validation failed", ErrorType.VALIDATION_ERROR),
          expectedType: ErrorType.VALIDATION_ERROR,
        },
        {
          error: new AGError("Network failed", ErrorType.NETWORK_ERROR),
          expectedType: ErrorType.NETWORK_ERROR,
        },
        {
          error: new Error("Generic error"),
          expectedType: "Error",
        },
      ];

      for (const testCase of testCases) {
        mockSupabase.rpc.mockResolvedValue({
          data: "error-id",
          error: null,
        });

        await errorReporter.reportError(testCase.error);

        expect(mockSupabase.rpc).toHaveBeenCalledWith("log_error", 
          expect.objectContaining({
            error_type: testCase.expectedType,
          })
        );

        vi.clearAllMocks();
      }
    });
  });

  describe("Context handling", () => {
    it("should merge error context with provided context", async () => {
      const agError = new AGError(
        "Test error",
        ErrorType.VALIDATION_ERROR,
        { errorField: "email" }
      );
      const additionalContext = { userId: "user-123", action: "signup" };

      mockSupabase.rpc.mockResolvedValue({
        data: "error-id",
        error: null,
      });

      await errorReporter.reportError(agError, additionalContext);

      expect(mockSupabase.rpc).toHaveBeenCalledWith("log_error",
        expect.objectContaining({
          context: JSON.stringify({
            errorField: "email",
            userId: "user-123",
            action: "signup",
          }),
          user_id: "user-123",
        })
      );
    });

    it("should handle context serialization errors", async () => {
      const error = new Error("Test error");
      const circularContext = { self: {} };
      circularContext.self = circularContext; // Create circular reference

      mockSupabase.rpc.mockResolvedValue({
        data: "error-id",
        error: null,
      });

      await errorReporter.reportError(error, circularContext);

      expect(mockSupabase.rpc).toHaveBeenCalledWith("log_error",
        expect.objectContaining({
          context: expect.stringContaining("Error serializing context"),
        })
      );
    });

    it("should extract user ID from context", async () => {
      const error = new Error("Test error");
      const contexts = [
        { userId: "user-123" },
        { user_id: "user-456" },
        { user: { id: "user-789" } },
        { currentUser: { id: "user-abc" } },
      ];

      const expectedUserIds = ["user-123", "user-456", "user-789", "user-abc"];

      for (let i = 0; i < contexts.length; i++) {
        mockSupabase.rpc.mockResolvedValue({
          data: "error-id",
          error: null,
        });

        await errorReporter.reportError(error, contexts[i]);

        expect(mockSupabase.rpc).toHaveBeenCalledWith("log_error",
          expect.objectContaining({
            user_id: expectedUserIds[i],
          })
        );

        vi.clearAllMocks();
      }
    });
  });

  describe("Error severity levels", () => {
    it("should handle different severity levels", async () => {
      const testCases = [
        { method: "reportError", level: "error" },
        { method: "reportWarning", level: "warning" },
        { method: "reportInfo", level: "info" },
      ];

      for (const testCase of testCases) {
        mockSupabase.rpc.mockResolvedValue({
          data: "log-id",
          error: null,
        });

        const message = `Test ${testCase.level}`;
        const context = { action: "test" };

        // @ts-expect-error - testing different methods
        await errorReporter[testCase.method](message, context);

        if (testCase.method === "reportError") {
          expect(mockConsoleError).toHaveBeenCalled();
        } else if (testCase.method === "reportWarning") {
          expect(mockConsoleWarn).toHaveBeenCalled();
        } else {
          expect(mockConsoleLog).toHaveBeenCalled();
        }

        vi.clearAllMocks();
      }
    });
  });

  describe("Batch error reporting", () => {
    it("should handle multiple errors efficiently", async () => {
      const errors = [
        new Error("Error 1"),
        new Error("Error 2"),
        new Error("Error 3"),
      ];

      mockSupabase.rpc.mockResolvedValue({
        data: "batch-id",
        error: null,
      });

      const promises = errors.map(error => errorReporter.reportError(error));
      const results = await Promise.all(promises);

      expect(results).toEqual(["batch-id", "batch-id", "batch-id"]);
      expect(mockSupabase.rpc).toHaveBeenCalledTimes(3);
    });

    it("should handle partial failures in batch reporting", async () => {
      const errors = [
        new Error("Error 1"),
        new Error("Error 2"),
        new Error("Error 3"),
      ];

      mockSupabase.rpc
        .mockResolvedValueOnce({ data: "success-1", error: null })
        .mockResolvedValueOnce({ data: null, error: { message: "Failed" } })
        .mockResolvedValueOnce({ data: "success-3", error: null });

      const promises = errors.map(error => errorReporter.reportError(error));
      const results = await Promise.all(promises);

      expect(results).toEqual(["success-1", null, "success-3"]);
    });
  });

  describe("Error filtering and throttling", () => {
    it("should throttle duplicate errors", async () => {
      const error = new Error("Repeated error");

      mockSupabase.rpc.mockResolvedValue({
        data: "error-id",
        error: null,
      });

      // Report the same error multiple times quickly
      await errorReporter.reportError(error);
      await errorReporter.reportError(error);
      await errorReporter.reportError(error);

      // Should only report once due to throttling
      expect(mockSupabase.rpc).toHaveBeenCalledTimes(1);
    });

    it("should filter out non-critical errors in production", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "production";

      const debugError = new AGError("Debug info", ErrorType.DEBUG_ERROR);

      mockSupabase.rpc.mockResolvedValue({
        data: "error-id",
        error: null,
      });

      await errorReporter.reportError(debugError);

      // Should not report debug errors in production
      expect(mockSupabase.rpc).not.toHaveBeenCalled();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe("Performance monitoring", () => {
    it("should track error reporting performance", async () => {
      const error = new Error("Performance test");

      mockSupabase.rpc.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({ data: "error-id", error: null }), 100)
        )
      );

      const startTime = Date.now();
      await errorReporter.reportError(error);
      const endTime = Date.now();

      expect(endTime - startTime).toBeGreaterThanOrEqual(100);
      expect(mockSupabase.rpc).toHaveBeenCalled();
    });
  });
});
