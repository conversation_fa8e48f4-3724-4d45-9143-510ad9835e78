import { describe, it, expect, beforeEach, vi } from "vitest";
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from "@/lib/services/error-handler";
import { ErrorReporter } from "@/lib/monitoring/error-reporter";
import { FallbackCache } from "@/lib/services/local-cache-fallback";
import { ErrorType } from "@/lib/services/error-types";
import { AGError } from "@/lib/services/error-classes";

// Mock the ErrorReporter
vi.mock("@/lib/monitoring/error-reporter", () => ({
  ErrorReporter: {
    getInstance: vi.fn().mockReturnValue({
      reportError: vi.fn().mockResolvedValue("mocked-error-id"),
      setUser: vi.fn(),
      addMetadata: vi.fn(),
    }),
  },
}));

// Mock the FallbackCache
vi.mock("@/lib/services/local-cache-fallback", () => ({
  FallbackCache: vi.fn().mockImplementation(() => ({
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
    clearExpired: vi.fn(),
  })),
}));

describe("ErrorHandler", () => {
  let errorHandler: ErrorHandler;
  let mockReporter: any;
  let mockCache: any;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Get fresh instances
    errorHandler = ErrorHandler.getInstance();
    mockReporter = ErrorReporter.getInstance();
    mockCache = new FallbackCache({ namespace: "test" });
  });

  describe("handleError", () => {
    it("should normalize and report a standard error", async () => {
      const originalError = new Error("Test error");

      const result = errorHandler.handleError(originalError);

      expect(result).toBeInstanceOf(AGError);
      expect(result.message).toBe("Test error");
      expect(result.type).toBe(ErrorType.UNKNOWN);
    });

    it("should handle AGError instances correctly", async () => {
      const agError = new AGError(
        "Custom AG error",
        ErrorType.VALIDATION,
        { field: "email" },
      );

      const result = errorHandler.handleError(agError);

      expect(result).toBe(agError);
      expect(result.type).toBe(ErrorType.VALIDATION);
      expect(result.metadata).toEqual({ field: "email" });
    });

    it("should handle network errors", async () => {
      const networkError = new Error("Network request failed");
      networkError.name = "NetworkError";

      const result = errorHandler.handleError(networkError);

      expect(result).toBeInstanceOf(AGError);
      expect(result.type).toBe(ErrorType.NETWORK);
      expect(result.message).toBe("Network request failed");
    });

    it("should handle authentication errors", async () => {
      const authError = new Error("Unauthorized");
      authError.name = "AuthenticationError";

      const result = errorHandler.handleError(authError);

      expect(result).toBeInstanceOf(AGError);
      expect(result.type).toBe(ErrorType.AUTHENTICATION);
      expect(result.message).toBe("Unauthorized");
    });

    it("should handle validation errors", async () => {
      const validationError = new Error("Invalid input");
      validationError.name = "ValidationError";

      const result = errorHandler.handleError(validationError);

      expect(result).toBeInstanceOf(AGError);
      expect(result.type).toBe(ErrorType.VALIDATION);
      expect(result.message).toBe("Invalid input");
    });

    it("should add context metadata when provided", async () => {
      const error = new Error("Test error");
      const context = { userId: "123", action: "save" };

      const result = errorHandler.handleError(error, context);

      expect(result.metadata).toEqual(context);
    });

    it("should report errors to the error reporter", async () => {
      const error = new Error("Test error");

      errorHandler.handleError(error);

      expect(mockReporter.reportError).toHaveBeenCalledWith(
        expect.any(AGError),
      );
    });

    it("should set user context when available", async () => {
      const error = new Error("Test error");
      const userContext = { id: "user-123", email: "<EMAIL>" };

      errorHandler.setUserContext(userContext);
      errorHandler.handleError(error);

      expect(mockReporter.setUser).toHaveBeenCalledWith(userContext);
    });
  });

  describe("recoverFromError", () => {
    it("should attempt recovery with fallback cache", async () => {
      const error = new AGError("Cache miss", ErrorType.CACHE);
      const fallbackData = { data: "fallback" };

      mockCache.get.mockReturnValue(fallbackData);

      const result = await errorHandler.recoverFromError(error, {
        cacheKey: "test-key",
        cache: mockCache,
      });

      expect(result).toEqual(fallbackData);
      expect(mockCache.get).toHaveBeenCalledWith("test-key");
    });

    it("should return null when no recovery is possible", async () => {
      const error = new AGError("Unrecoverable", ErrorType.UNKNOWN);

      mockCache.get.mockReturnValue(null);

      const result = await errorHandler.recoverFromError(error, {
        cacheKey: "test-key",
        cache: mockCache,
      });

      expect(result).toBeNull();
    });

    it("should execute custom recovery function", async () => {
      const error = new AGError("Custom error", ErrorType.VALIDATION);
      const recoveryData = { recovered: true };
      const customRecovery = vi.fn().mockResolvedValue(recoveryData);

      const result = await errorHandler.recoverFromError(error, {
        customRecovery,
      });

      expect(result).toEqual(recoveryData);
      expect(customRecovery).toHaveBeenCalledWith(error);
    });
  });

  describe("isRetryableError", () => {
    it("should identify network errors as retryable", () => {
      const networkError = new AGError("Network error", ErrorType.NETWORK);

      const result = errorHandler.isRetryableError(networkError);

      expect(result).toBe(true);
    });

    it("should identify rate limit errors as retryable", () => {
      const rateLimitError = new AGError(
        "Rate limited",
        ErrorType.RATE_LIMIT,
      );

      const result = errorHandler.isRetryableError(rateLimitError);

      expect(result).toBe(true);
    });

    it("should identify validation errors as non-retryable", () => {
      const validationError = new AGError(
        "Invalid input",
        ErrorType.VALIDATION,
      );

      const result = errorHandler.isRetryableError(validationError);

      expect(result).toBe(false);
    });

    it("should identify authentication errors as non-retryable", () => {
      const authError = new AGError(
        "Unauthorized",
        ErrorType.AUTHENTICATION,
      );

      const result = errorHandler.isRetryableError(authError);

      expect(result).toBe(false);
    });
  });

  describe("clearUserContext", () => {
    it("should clear user context from error reporter", () => {
      errorHandler.clearUserContext();

      expect(mockReporter.setUser).toHaveBeenCalledWith(null);
    });
  });

  describe("addGlobalMetadata", () => {
    it("should add metadata to error reporter", () => {
      const metadata = { version: "1.0.0", environment: "test" };

      errorHandler.addGlobalMetadata(metadata);

      expect(mockReporter.addMetadata).toHaveBeenCalledWith(metadata);
    });
  });
});
