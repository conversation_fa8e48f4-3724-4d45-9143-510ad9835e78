/**
 * Test file for TaskService
 * Using a mock approach similar to document-service tests
 */

import { describe, it, expect, beforeEach, vi } from "vitest";

// Create a mock TaskService
const mockTaskService = {
  getAll: vi.fn(),
  getById: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  getHistory: vi.fn(),
};

// Create mock UserService that TaskService depends on
const mockUserService = {
  getAll: vi.fn(),
  getById: vi.fn(),
  getByAuthId: vi.fn(),
  getByIds: vi.fn(),
};

// Mock the user-service module
vi.mock("@/lib/services/user-service", () => ({
  createUserService: vi.fn(() => mockUserService),
  UserService: vi.fn(),
}));

// Mock the task-service module
vi.mock("@/lib/services/task-service", () => {
  return {
    TaskService: vi.fn(() => mockTaskService),
    createTaskService: vi.fn(() => mockTaskService),
  };
});

describe("TaskService", () => {
  const tenantId = "test-tenant-123";
  const userId = "user-123";

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  describe("getAll", () => {
    it("retrieves all tasks with related data", async () => {
      // Mock task data
      const mockTasks = [
        {
          id: "task-1",
          title: "Review contract",
          description: "Review the client contract for legal compliance",
          status: "pending",
          priority: "high",
          due_date: "2024-02-15T10:00:00Z",
          created_at: "2024-01-15T10:00:00Z",
          updated_at: "2024-01-15T10:00:00Z",
          tenant_id: tenantId,
          assigned_to: userId,
          created_by: userId,
          assignee: {
            id: userId,
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
          },
          creator: {
            id: userId,
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
          },
        },
        {
          id: "task-2",
          title: "Prepare documents",
          description: "Prepare legal documents for court filing",
          status: "in_progress",
          priority: "medium",
          due_date: "2024-02-20T15:00:00Z",
          created_at: "2024-01-16T09:00:00Z",
          updated_at: "2024-01-16T09:00:00Z",
          tenant_id: tenantId,
          assigned_to: userId,
          created_by: userId,
          assignee: {
            id: userId,
            first_name: "Jane",
            last_name: "Smith",
            email: "<EMAIL>",
          },
          creator: {
            id: userId,
            first_name: "Jane",
            last_name: "Smith",
            email: "<EMAIL>",
          },
        },
      ];

      mockTaskService.getAll.mockResolvedValue(mockTasks);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.getAll(tenantId);

      expect(result).toEqual(mockTasks);
      expect(mockTaskService.getAll).toHaveBeenCalledWith(tenantId);
    });

    it("handles database errors gracefully", async () => {
      mockTaskService.getAll.mockRejectedValue(new Error("Database error"));

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();

      await expect(taskService.getAll(tenantId)).rejects.toThrow(
        "Database error",
      );
    });
  });

  describe("getById", () => {
    it("retrieves a single task with all related data", async () => {
      const mockTask = {
        id: "task-1",
        title: "Review contract",
        description: "Review the client contract for legal compliance",
        status: "pending",
        priority: "high",
        due_date: "2024-02-15T10:00:00Z",
        created_at: "2024-01-15T10:00:00Z",
        updated_at: "2024-01-15T10:00:00Z",
        tenant_id: tenantId,
        assigned_to: userId,
        created_by: userId,
        assignee: {
          id: userId,
          first_name: "John",
          last_name: "Doe",
          email: "<EMAIL>",
        },
        creator: {
          id: userId,
          first_name: "John",
          last_name: "Doe",
          email: "<EMAIL>",
        },
      };

      mockTaskService.getById.mockResolvedValue(mockTask);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.getById("task-1", tenantId);

      expect(result).toEqual(mockTask);
      expect(mockTaskService.getById).toHaveBeenCalledWith("task-1", tenantId);
    });

    it("returns null when task is not found", async () => {
      mockTaskService.getById.mockResolvedValue(null);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.getById("nonexistent", tenantId);

      expect(result).toBeNull();
    });
  });

  describe("create", () => {
    it("creates a new task successfully", async () => {
      const taskData = {
        title: "New task",
        description: "A new task description",
        status: "pending",
        priority: "medium",
        due_date: "2024-02-20T10:00:00Z",
        assigned_to: userId,
        created_by: userId,
      };

      const mockCreatedTask = {
        id: "task-new",
        ...taskData,
        tenant_id: tenantId,
        created_at: "2024-01-17T10:00:00Z",
        updated_at: "2024-01-17T10:00:00Z",
      };

      mockTaskService.create.mockResolvedValue(mockCreatedTask);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.create(taskData, tenantId);

      expect(result).toEqual(mockCreatedTask);
      expect(mockTaskService.create).toHaveBeenCalledWith(taskData, tenantId);
    });

    it("throws error when title is missing", async () => {
      const taskData = {
        description: "A task without title",
        status: "pending",
        priority: "medium",
        assigned_to: userId,
        created_by: userId,
      };

      mockTaskService.create.mockRejectedValue(
        new Error("Title is required"),
      );

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();

      await expect(taskService.create(taskData, tenantId)).rejects.toThrow(
        "Title is required",
      );
    });
  });

  describe("update", () => {
    it("updates an existing task", async () => {
      const updateData = {
        title: "Updated task title",
        status: "completed",
        priority: "low",
      };

      const mockUpdatedTask = {
        id: "task-1",
        title: "Updated task title",
        description: "Original description",
        status: "completed",
        priority: "low",
        due_date: "2024-02-15T10:00:00Z",
        created_at: "2024-01-15T10:00:00Z",
        updated_at: "2024-01-17T11:00:00Z",
        tenant_id: tenantId,
        assigned_to: userId,
        created_by: userId,
      };

      mockTaskService.update.mockResolvedValue(mockUpdatedTask);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.update("task-1", updateData, tenantId);

      expect(result).toEqual(mockUpdatedTask);
      expect(mockTaskService.update).toHaveBeenCalledWith(
        "task-1",
        updateData,
        tenantId,
      );
    });

    it("returns null when task to update is not found", async () => {
      const updateData = { title: "Updated title" };

      mockTaskService.update.mockResolvedValue(null);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.update(
        "nonexistent",
        updateData,
        tenantId,
      );

      expect(result).toBeNull();
    });
  });

  describe("delete", () => {
    it("deletes a task successfully", async () => {
      mockTaskService.delete.mockResolvedValue(true);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.delete("task-1", tenantId);

      expect(result).toBe(true);
      expect(mockTaskService.delete).toHaveBeenCalledWith("task-1", tenantId);
    });

    it("returns false when task to delete is not found", async () => {
      mockTaskService.delete.mockResolvedValue(false);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.delete("nonexistent", tenantId);

      expect(result).toBe(false);
    });

    it("handles database errors gracefully", async () => {
      mockTaskService.delete.mockRejectedValue(new Error("Database error"));

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();

      await expect(taskService.delete("task-1", tenantId)).rejects.toThrow(
        "Database error",
      );
    });
  });

  describe("getHistory", () => {
    it("retrieves task history with user data", async () => {
      const taskId = "task-123";

      // Mock history data
      const mockHistory = [
        {
          id: "history-1",
          task_id: taskId,
          tenant_id: tenantId,
          changed_by: "user-123",
          changed_at: "2025-04-01T10:00:00Z",
          change_type: "created",
          new_values: { title: "New Task", status: "todo" },
          changed_by_user: {
            id: "user-123",
            email: "<EMAIL>",
            first_name: "John",
            last_name: "Doe",
            full_name: "John Doe",
          },
        },
        {
          id: "history-2",
          task_id: taskId,
          tenant_id: tenantId,
          changed_by: "user-456",
          changed_at: "2025-04-02T14:30:00Z",
          change_type: "updated",
          previous_values: { status: "todo" },
          new_values: { status: "in_progress" },
          changed_by_user: {
            id: "user-456",
            email: "<EMAIL>",
            first_name: "Jane",
            last_name: "Smith",
            full_name: "Jane Smith",
          },
        },
      ];

      mockTaskService.getHistory.mockResolvedValue(mockHistory);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.getHistory(taskId, tenantId);

      expect(result).toHaveLength(2);
      expect(result[0].change_type).toBe("created");
      expect(result[0].changed_by_user).toBeDefined();
      expect(result[1].change_type).toBe("updated");
      expect(result[1].changed_by_user).toBeDefined();
      expect(mockTaskService.getHistory).toHaveBeenCalledWith(taskId, tenantId);
    });

    it("returns empty array when no history exists", async () => {
      mockTaskService.getHistory.mockResolvedValue([]);

      const { TaskService } = await import("@/lib/services/task-service");
      const taskService = new TaskService();
      const result = await taskService.getHistory("no-history-task", tenantId);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });
  });
});
