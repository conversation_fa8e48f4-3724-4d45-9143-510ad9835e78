/**
 * Test file for DocumentService
 * Using a manual mock approach for more reliable testing
 */

import { describe, it, expect, beforeEach, vi } from "vitest";

// Create a mock DocumentService
const mockDocumentService = {
  getAll: vi.fn(),
  getById: vi.fn(),
  upload: vi.fn(),
  updateMetadata: vi.fn(),
  delete: vi.fn(),
  getDownloadUrl: vi.fn(),
  analyzeDocumentWithGemini: vi.fn(),
  getDocumentAnalyses: vi.fn(),
  analyzeMedicalForm: vi.fn(),
  extractTasksFromDocument: vi.fn(),
};

// Use the mock instead of the real service
vi.mock("@/lib/services/document-service", async () => {
  const actual = await vi.importActual<any>("@/lib/services/document-service");
  return {
    DocumentService: vi.fn(() => mockDocumentService),
    DocumentMetadataSchema: actual.DocumentMetadataSchema,
  };
});

describe("DocumentService", () => {
  const tenantId = "test-tenant-123";

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  describe("getAll", () => {
    it("retrieves documents with correct filters", async () => {
      // Setup mock response
      const expectedResult = {
        documents: [
          { id: "doc1", title: "Document 1" },
          { id: "doc2", title: "Document 2" },
        ],
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      mockDocumentService.getAll.mockResolvedValue(expectedResult);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.getAll(tenantId, {
        page: 1,
        limit: 10,
      });

      expect(result).toEqual(expectedResult);
      expect(mockDocumentService.getAll).toHaveBeenCalledWith(tenantId, {
        page: 1,
        limit: 10,
      });
    });

    it("handles empty results", async () => {
      const expectedResult = {
        documents: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };

      mockDocumentService.getAll.mockResolvedValue(expectedResult);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.getAll(tenantId);

      expect(result.documents).toHaveLength(0);
      expect(result.total).toBe(0);
    });

    it("handles database errors gracefully", async () => {
      mockDocumentService.getAll.mockRejectedValue(new Error("Database error"));

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();

      await expect(documentService.getAll(tenantId)).rejects.toThrow(
        "Database error",
      );
    });
  });

  describe("getById", () => {
    it("retrieves a single document by ID", async () => {
      const mockDocument = {
        id: "doc1",
        title: "Test Document",
        file_name: "test.pdf",
        file_size: 1024,
        mime_type: "application/pdf",
        tenant_id: tenantId,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      };

      mockDocumentService.getById.mockResolvedValue(mockDocument);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.getById("doc1", tenantId);

      expect(result).toEqual(mockDocument);
      expect(mockDocumentService.getById).toHaveBeenCalledWith("doc1", tenantId);
    });

    it("returns null when document is not found", async () => {
      mockDocumentService.getById.mockResolvedValue(null);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.getById("nonexistent", tenantId);

      expect(result).toBeNull();
    });
  });

  describe("upload", () => {
    it("uploads a document successfully", async () => {
      const file = new File(["test content"], "test.pdf", {
        type: "application/pdf",
      });
      const metadata = {
        title: "Test Document",
        description: "A test document",
      };

      const mockUploadResult = {
        id: "doc-new",
        title: "Test Document",
        file_name: "test.pdf",
        file_size: file.size,
        mime_type: "application/pdf",
        tenant_id: tenantId,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      };

      mockDocumentService.upload.mockResolvedValue(mockUploadResult);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.upload(file, metadata, tenantId);

      expect(result).toEqual(mockUploadResult);
      expect(mockDocumentService.upload).toHaveBeenCalledWith(
        file,
        metadata,
        tenantId,
      );
    });

    it("handles upload errors", async () => {
      const file = new File(["test content"], "test.pdf", {
        type: "application/pdf",
      });

      mockDocumentService.upload.mockRejectedValue(new Error("Upload failed"));

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();

      await expect(
        documentService.upload(file, {}, tenantId),
      ).rejects.toThrow("Upload failed");
    });
  });

  describe("updateMetadata", () => {
    it("updates document metadata successfully", async () => {
      const updateData = {
        title: "Updated Title",
        description: "Updated description",
      };

      const mockUpdatedDocument = {
        id: "doc1",
        title: "Updated Title",
        description: "Updated description",
        file_name: "test.pdf",
        tenant_id: tenantId,
        updated_at: "2024-01-02T00:00:00Z",
      };

      mockDocumentService.updateMetadata.mockResolvedValue(
        mockUpdatedDocument,
      );

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.updateMetadata(
        "doc1",
        updateData,
        tenantId,
      );

      expect(result).toEqual(mockUpdatedDocument);
      expect(mockDocumentService.updateMetadata).toHaveBeenCalledWith(
        "doc1",
        updateData,
        tenantId,
      );
    });

    it("returns null when document to update is not found", async () => {
      mockDocumentService.updateMetadata.mockResolvedValue(null);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.updateMetadata(
        "nonexistent",
        { title: "New Title" },
        tenantId,
      );

      expect(result).toBeNull();
    });
  });

  describe("delete", () => {
    it("deletes a document successfully", async () => {
      mockDocumentService.delete.mockResolvedValue(true);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.delete("doc1", tenantId);

      expect(result).toBe(true);
      expect(mockDocumentService.delete).toHaveBeenCalledWith("doc1", tenantId);
    });

    it("returns false when document to delete is not found", async () => {
      mockDocumentService.delete.mockResolvedValue(false);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.delete("nonexistent", tenantId);

      expect(result).toBe(false);
    });
  });

  describe("getDownloadUrl", () => {
    it("generates download URL for document", async () => {
      const mockUrl = "https://example.com/download/doc1";

      mockDocumentService.getDownloadUrl.mockResolvedValue(mockUrl);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.getDownloadUrl("doc1", tenantId);

      expect(result).toBe(mockUrl);
      expect(mockDocumentService.getDownloadUrl).toHaveBeenCalledWith(
        "doc1",
        tenantId,
      );
    });

    it("returns null for invalid document", async () => {
      mockDocumentService.getDownloadUrl.mockResolvedValue(null);

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.getDownloadUrl(
        "nonexistent",
        tenantId,
      );

      expect(result).toBeNull();
    });
  });

  describe("analyzeDocumentWithGemini", () => {
    it("analyzes document with AI successfully", async () => {
      const mockAnalysis = {
        id: "analysis-1",
        document_id: "doc1",
        analysis_type: "gemini",
        results: {
          summary: "Document summary",
          key_points: ["Point 1", "Point 2"],
        },
        created_at: "2024-01-01T00:00:00Z",
      };

      mockDocumentService.analyzeDocumentWithGemini.mockResolvedValue(
        mockAnalysis,
      );

      const { DocumentService } = await import("@/lib/services/document-service");
      const documentService = new DocumentService();
      const result = await documentService.analyzeDocumentWithGemini(
        "doc1",
        tenantId,
      );

      expect(result).toEqual(mockAnalysis);
      expect(
        mockDocumentService.analyzeDocumentWithGemini,
      ).toHaveBeenCalledWith("doc1", tenantId);
    });
  });
});
