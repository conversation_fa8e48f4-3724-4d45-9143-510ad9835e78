/**
 * Test file for UserService
 * Using a mock approach similar to document-service tests
 */

import { describe, it, expect, beforeEach, vi } from "vitest";

// Create a mock UserService
const mockUserService = {
  getAll: vi.fn(),
  getById: vi.fn(),
  getByAuthId: vi.fn(),
  getByIds: vi.fn(),
};

// Mock the user-service module
vi.mock("@/lib/services/user-service", () => {
  return {
    UserService: vi.fn(() => mockUserService),
    createUserService: vi.fn(() => mockUserService),
  };
});

describe("UserService", () => {
  const tenantId = "test-tenant-123";

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  describe("getAll", () => {
    it("retrieves all users for a tenant", async () => {
      const mockUsers = [
        {
          id: "user-1",
          auth_user_id: "auth-1",
          tenant_id: tenantId,
          first_name: "<PERSON>",
          last_name: "Do<PERSON>",
          email: "<EMAIL>",
          role: "user",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
        {
          id: "user-2",
          auth_user_id: "auth-2",
          tenant_id: tenantId,
          first_name: "Jane",
          last_name: "Smith",
          email: "<EMAIL>",
          role: "admin",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
      ];

      mockUserService.getAll.mockResolvedValue(mockUsers);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getAll(tenantId);

      expect(result).toEqual(mockUsers);
      expect(mockUserService.getAll).toHaveBeenCalledWith(tenantId);
    });

    it("handles users with missing names properly", async () => {
      const mockUsers = [
        {
          id: "user-1",
          auth_user_id: "auth-1",
          tenant_id: tenantId,
          first_name: null,
          last_name: null,
          email: "<EMAIL>",
          role: "user",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
      ];

      mockUserService.getAll.mockResolvedValue(mockUsers);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getAll(tenantId);

      expect(result).toEqual(mockUsers);
      expect(result[0].first_name).toBeNull();
      expect(result[0].last_name).toBeNull();
    });

    it("handles database errors gracefully", async () => {
      mockUserService.getAll.mockRejectedValue(new Error("Database error"));

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();

      await expect(userService.getAll(tenantId)).rejects.toThrow(
        "Database error",
      );
    });
  });

  describe("getById", () => {
    it("retrieves a single user by ID", async () => {
      const mockUser = {
        id: "user-1",
        auth_user_id: "auth-1",
        tenant_id: tenantId,
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        role: "user",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      };

      mockUserService.getById.mockResolvedValue(mockUser);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getById("user-1", tenantId);

      expect(result).toEqual(mockUser);
      expect(mockUserService.getById).toHaveBeenCalledWith("user-1", tenantId);
    });

    it("returns null when user is not found", async () => {
      mockUserService.getById.mockResolvedValue(null);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getById("nonexistent", tenantId);

      expect(result).toBeNull();
    });

    it("handles database errors by returning null", async () => {
      mockUserService.getById.mockRejectedValue(new Error("Database error"));

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getById("user-1", tenantId);

      expect(result).toBeNull();
    });
  });

  describe("getByAuthId", () => {
    it("retrieves a user by auth_user_id", async () => {
      const mockUser = {
        id: "user-1",
        auth_user_id: "auth-1",
        tenant_id: tenantId,
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        role: "user",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      };

      mockUserService.getByAuthId.mockResolvedValue(mockUser);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getByAuthId("auth-1", tenantId);

      expect(result).toEqual(mockUser);
      expect(mockUserService.getByAuthId).toHaveBeenCalledWith(
        "auth-1",
        tenantId,
      );
    });

    it("returns null when auth user ID is not found", async () => {
      mockUserService.getByAuthId.mockResolvedValue(null);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getByAuthId("nonexistent", tenantId);

      expect(result).toBeNull();
    });
  });

  describe("getByIds", () => {
    it("retrieves multiple users by their IDs", async () => {
      const mockUsers = [
        {
          id: "user-1",
          auth_user_id: "auth-1",
          tenant_id: tenantId,
          first_name: "John",
          last_name: "Doe",
          email: "<EMAIL>",
          role: "user",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
        {
          id: "user-2",
          auth_user_id: "auth-2",
          tenant_id: tenantId,
          first_name: "Jane",
          last_name: "Smith",
          email: "<EMAIL>",
          role: "admin",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
        },
      ];

      mockUserService.getByIds.mockResolvedValue(mockUsers);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getByIds(["user-1", "user-2"], tenantId);

      expect(result).toEqual(mockUsers);
      expect(mockUserService.getByIds).toHaveBeenCalledWith(
        ["user-1", "user-2"],
        tenantId,
      );
    });

    it("returns empty array for empty input", async () => {
      mockUserService.getByIds.mockResolvedValue([]);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getByIds([], tenantId);

      expect(result).toEqual([]);
      expect(mockUserService.getByIds).toHaveBeenCalledWith([], tenantId);
    });

    it("handles the case when some users are not found", async () => {
      const userIds = ["user-1", "nonexistent", "user-3"];

      // Mock users data - only 2 of the 3 requested users
      const mockUsers = [
        {
          id: "user-1",
          email: "<EMAIL>",
          first_name: "John",
          last_name: "Doe",
          role: "partner",
          auth_user_id: "auth-1",
          full_name: "John Doe",
        },
        {
          id: "user-3",
          email: "<EMAIL>",
          first_name: "Alex",
          last_name: "Johnson",
          role: "paralegal",
          auth_user_id: "auth-3",
          full_name: "Alex Johnson",
        },
      ];

      mockUserService.getByIds.mockResolvedValue(mockUsers);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getByIds(userIds, tenantId);

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe("user-1");
      expect(result[1].id).toBe("user-3");
      expect(mockUserService.getByIds).toHaveBeenCalledWith(userIds, tenantId);
    });

    it("returns empty array on database error", async () => {
      const userIds = ["user-1", "user-2"];

      mockUserService.getByIds.mockResolvedValue([]);

      const { UserService } = await import("@/lib/services/user-service");
      const userService = new UserService();
      const result = await userService.getByIds(userIds, tenantId);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });
  });
});
