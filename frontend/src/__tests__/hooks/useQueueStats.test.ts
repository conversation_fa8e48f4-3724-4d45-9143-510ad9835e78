import { describe, it, expect, beforeEach, vi } from "vitest";
import { renderHook, waitFor } from "@testing-library/react";

// Mock dependencies
vi.mock("@/hooks/useAuthenticatedFetch", () => ({
  useAuthenticatedFetch: vi.fn(),
}));

vi.mock("@/components/ui/use-toast", () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

vi.mock("@/lib/env/client", () => ({
  clientEnv: {
    NEXT_PUBLIC_QUEUE_POLLING_INTERVAL: "5000",
  },
}));

// Mock SWR
vi.mock("swr", () => {
  const mockSwr = vi.fn((key, fetcher, options) => {
    const mockData = {
      tenant_id: "test-tenant",
      queued: 5,
      processing: 2,
      completed: 100,
      failed: 3,
      total_today: 103,
      avg_processing_time_seconds: 45.5,
      last_updated: "2024-01-15T10:30:00Z",
      worker_status: "healthy",
    };

    return {
      data: key ? mockData : undefined,
      error: null,
      isLoading: false,
      mutate: vi.fn().mockResolvedValue(mockData),
    };
  });

  return {
    default: mockSwr,
  };
});

import { useQueueStats } from "@/hooks/useQueueStats";
import { useAuthenticatedFetch } from "@/hooks/useAuthenticatedFetch";

const mockUseAuthenticatedFetch = vi.mocked(useAuthenticatedFetch);

describe("useQueueStats", () => {
  const mockAuthedFetch = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseAuthenticatedFetch.mockReturnValue({
      authedFetch: mockAuthedFetch,
      isReady: true,
      error: null,
    });
  });

  it("returns queue stats when authenticated", async () => {
    const { result } = renderHook(() => useQueueStats());

    await waitFor(() => {
      expect(result.current.data).toBeTruthy();
      expect(result.current.data?.tenant_id).toBe("test-tenant");
      expect(result.current.data?.queued).toBe(5);
      expect(result.current.data?.processing).toBe(2);
    });
  });

  it("provides clearQueue function", async () => {
    mockAuthedFetch.mockResolvedValue({});

    const { result } = renderHook(() => useQueueStats());

    await waitFor(() => {
      expect(result.current.clearQueue).toBeDefined();
    });

    await result.current.clearQueue();

    expect(mockAuthedFetch).toHaveBeenCalledWith("/api/v1/queue/clear", {
      method: "POST",
    });
  });

  it("handles clearQueue errors", async () => {
    mockAuthedFetch.mockRejectedValue(new Error("Clear failed"));

    const { result } = renderHook(() => useQueueStats());

    await waitFor(() => {
      expect(result.current.clearQueue).toBeDefined();
    });

    try {
      await result.current.clearQueue();
      expect(true).toBe(false); // Should not reach here
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
      expect((error as Error).message).toBe("Clear failed");
    }
  });

  it("does not fetch when not ready", () => {
    mockUseAuthenticatedFetch.mockReturnValue({
      authedFetch: mockAuthedFetch,
      isReady: false,
      error: null,
    });

    const { result } = renderHook(() => useQueueStats());

    expect(result.current.data).toBeNull();
    expect(result.current.isLoading).toBe(false);
  });
});
