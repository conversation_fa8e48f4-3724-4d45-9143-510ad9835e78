/**
 * Integration Test Setup
 * 
 * Global setup for integration tests including MSW server, test utilities,
 * and environment configuration.
 */

import '@testing-library/jest-dom';
import { beforeAll, afterEach, afterAll, vi } from 'vitest';
import { cleanup } from '@testing-library/react';

// Import test infrastructure
import { server, MSWTestUtils } from './infra/http/msw-server';
import { timeController } from './infra/utils/time';
import { AuditLoggerSpy } from './infra/compliance/audit-logger.spy';
import { LLMProviderRegistry } from './infra/ai/llm-stub';

/**
 * Global test state
 */
interface GlobalTestState {
  auditLogger: AuditLoggerSpy;
  startTime: Date;
  testCount: number;
}

declare global {
  var __INTEGRATION_TEST_STATE__: GlobalTestState;
}

/**
 * Setup MSW server before all tests
 */
beforeAll(() => {
  // Start MSW server
  MSWTestUtils.start();
  
  // Initialize global test state
  global.__INTEGRATION_TEST_STATE__ = {
    auditLogger: new AuditLoggerSpy(),
    startTime: new Date(),
    testCount: 0
  };
  
  // Setup console overrides for cleaner test output
  setupConsoleOverrides();
  
  // Setup global error handlers
  setupErrorHandlers();
  
  // Setup environment variables
  setupTestEnvironment();
  
  console.log('🧪 Integration test environment initialized');
});

/**
 * Cleanup after each test
 */
afterEach(() => {
  // Cleanup React Testing Library
  cleanup();
  
  // Reset MSW handlers to default state
  MSWTestUtils.reset();
  
  // Clear audit logger
  global.__INTEGRATION_TEST_STATE__.auditLogger.clear();
  
  // Clear LLM provider registry
  LLMProviderRegistry.clearTestProvider();
  
  // Restore real timers if fake timers were used
  timeController.useRealTimers();
  
  // Clear all mocks
  vi.clearAllMocks();
  
  // Reset modules
  vi.resetModules();
  
  // Increment test count
  global.__INTEGRATION_TEST_STATE__.testCount++;
});

/**
 * Cleanup after all tests
 */
afterAll(() => {
  // Stop MSW server
  MSWTestUtils.stop();
  
  // Final cleanup
  timeController.useRealTimers();
  
  const { testCount, startTime } = global.__INTEGRATION_TEST_STATE__;
  const duration = Date.now() - startTime.getTime();
  
  console.log(`✅ Integration tests completed: ${testCount} tests in ${duration}ms`);
});

/**
 * Setup console overrides for cleaner test output
 */
function setupConsoleOverrides(): void {
  // Suppress noisy console output in tests
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;
  
  console.warn = (...args: any[]) => {
    // Filter out known noisy warnings
    const message = args[0]?.toString() || '';
    
    if (
      message.includes('React Router') ||
      message.includes('Warning: ReactDOM.render') ||
      message.includes('Warning: componentWillReceiveProps') ||
      message.includes('act()')
    ) {
      return; // Suppress
    }
    
    originalConsoleWarn(...args);
  };
  
  console.error = (...args: any[]) => {
    // Filter out known test errors that are expected
    const message = args[0]?.toString() || '';
    
    if (
      message.includes('Error: Not implemented') ||
      message.includes('ResizeObserver loop limit exceeded')
    ) {
      return; // Suppress
    }
    
    originalConsoleError(...args);
  };
}

/**
 * Setup global error handlers
 */
function setupErrorHandlers(): void {
  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    // Don't exit the process in tests
  });
  
  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    // Don't exit the process in tests
  });
}

/**
 * Setup test environment variables
 */
function setupTestEnvironment(): void {
  // Ensure test environment
  process.env.NODE_ENV = 'test';
  process.env.VITEST_INTEGRATION = 'true';
  
  // Mock external service URLs
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'http://localhost:54321';
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';
  process.env.OPENAI_API_KEY = 'test-openai-key';
  
  // Disable external APIs
  process.env.DISABLE_EXTERNAL_APIS = 'true';
  
  // Test-specific configuration
  process.env.NEXT_PUBLIC_APP_ENV = 'test';
  process.env.NEXT_PUBLIC_ENABLE_ANALYTICS = 'false';
  process.env.NEXT_PUBLIC_ENABLE_SENTRY = 'false';
}

/**
 * Global test utilities available in all integration tests
 */
export const IntegrationTestUtils = {
  /**
   * Get global audit logger instance
   */
  getAuditLogger: (): AuditLoggerSpy => {
    return global.__INTEGRATION_TEST_STATE__.auditLogger;
  },
  
  /**
   * Get test execution stats
   */
  getTestStats: () => {
    const { testCount, startTime } = global.__INTEGRATION_TEST_STATE__;
    return {
      testCount,
      duration: Date.now() - startTime.getTime(),
      startTime
    };
  },
  
  /**
   * Wait for async operations to complete
   */
  waitForAsync: async (timeout: number = 5000): Promise<void> => {
    return new Promise((resolve) => {
      setTimeout(resolve, timeout);
    });
  },
  
  /**
   * Wait for element to appear with timeout
   */
  waitForElement: async (
    selector: string,
    timeout: number = 5000
  ): Promise<Element | null> => {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const element = document.querySelector(selector);
      if (element) {
        return element;
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return null;
  },
  
  /**
   * Mock fetch responses for specific URLs
   */
  mockFetch: (url: string, response: any, status: number = 200) => {
    global.fetch = vi.fn().mockImplementation((requestUrl) => {
      if (requestUrl.includes(url)) {
        return Promise.resolve({
          ok: status >= 200 && status < 300,
          status,
          json: () => Promise.resolve(response),
          text: () => Promise.resolve(JSON.stringify(response))
        });
      }
      
      // Default mock response
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        text: () => Promise.resolve('{}')
      });
    });
  },
  
  /**
   * Reset all mocks to clean state
   */
  resetMocks: () => {
    vi.clearAllMocks();
    MSWTestUtils.reset();
    global.__INTEGRATION_TEST_STATE__.auditLogger.clear();
    LLMProviderRegistry.clearTestProvider();
  },
  
  /**
   * Setup test user session
   */
  setupTestSession: (userId: string = 'test-user-1', tenantId: string = 'test-tenant-1') => {
    // Mock session data that would normally come from Supabase
    const sessionData = {
      user: {
        id: userId,
        email: '<EMAIL>',
        role: 'attorney'
      },
      tenantId,
      permissions: ['read:documents', 'write:documents', 'read:cases']
    };
    
    // Store in global state for test access
    (global as any).__TEST_SESSION__ = sessionData;
    
    return sessionData;
  },
  
  /**
   * Clear test session
   */
  clearTestSession: () => {
    delete (global as any).__TEST_SESSION__;
  }
};

/**
 * Custom matchers for integration tests
 */
declare global {
  namespace Vi {
    interface JestAssertion<T = any> {
      toHaveBeenCalledWithPhi(): T;
      toHaveAuditLog(eventType: string): T;
      toBeWithinTimeRange(start: Date, end: Date): T;
    }
  }
}

// Extend expect with custom matchers
expect.extend({
  toHaveBeenCalledWithPhi(received: any) {
    const calls = received.mock?.calls || [];
    const hasPhiCall = calls.some((call: any[]) => {
      const args = call.join(' ').toLowerCase();
      return args.includes('ssn') || 
             args.includes('phone') || 
             args.includes('email') ||
             args.includes('address');
    });
    
    return {
      message: () => `Expected function to ${hasPhiCall ? 'not ' : ''}have been called with PHI data`,
      pass: hasPhiCall
    };
  },
  
  toHaveAuditLog(received: AuditLoggerSpy, eventType: string) {
    const events = received.getEventsByType(eventType as any);
    
    return {
      message: () => `Expected audit logger to ${events.length > 0 ? 'not ' : ''}have ${eventType} events`,
      pass: events.length > 0
    };
  },
  
  toBeWithinTimeRange(received: Date, start: Date, end: Date) {
    const isWithin = received >= start && received <= end;
    
    return {
      message: () => `Expected ${received.toISOString()} to ${isWithin ? 'not ' : ''}be within ${start.toISOString()} and ${end.toISOString()}`,
      pass: isWithin
    };
  }
});

// Export for use in tests
export { server, MSWTestUtils } from './infra/http/msw-server';
export { timeController, TimeTestHelpers } from './infra/utils/time';
export { AuditLoggerSpy } from './infra/compliance/audit-logger.spy';
export { PhiRedactor } from './infra/compliance/redaction';
export { LLMStubFactory, LLMProviderRegistry } from './infra/ai/llm-stub';
