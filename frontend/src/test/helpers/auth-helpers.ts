/**
 * Authentication Test Helpers
 * 
 * Provides utilities for testing authentication, authorization, and payment auth
 * scenarios with proper mocking and per-test overrides.
 */

import { vi } from "vitest";

/**
 * Creates a mock user profile for testing
 */
export function createMockUser(overrides: Partial<any> = {}) {
  return {
    id: "user-123",
    email: "<EMAIL>",
    tenant_id: "tenant-123",
    role: "user",
    first_name: "Test",
    last_name: "User",
    is_super_admin: false,
    app_metadata: {
      is_super_admin: false,
      tenant_id: "tenant-123",
    },
    user_metadata: {},
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    ...overrides,
  };
}

/**
 * Creates a mock super admin user
 */
export function createMockSuperAdmin(overrides: Partial<any> = {}) {
  return createMockUser({
    role: "super_admin",
    is_super_admin: true,
    app_metadata: {
      is_super_admin: true,
      tenant_id: "tenant-123",
    },
    ...overrides,
  });
}

/**
 * Creates a mock partner user
 */
export function createMockPartner(overrides: Partial<any> = {}) {
  return createMockUser({
    role: "partner",
    ...overrides,
  });
}

/**
 * Creates a mock attorney user
 */
export function createMockAttorney(overrides: Partial<any> = {}) {
  return createMockUser({
    role: "attorney",
    ...overrides,
  });
}

/**
 * Creates a mock paralegal user
 */
export function createMockParalegal(overrides: Partial<any> = {}) {
  return createMockUser({
    role: "paralegal",
    ...overrides,
  });
}

/**
 * Payment authorization result types
 */
export type PaymentAuthResult = 
  | { ok: true; user: any; tenant: any }
  | { ok: false; code: 'UNAUTHORIZED' | 'INSUFFICIENT_PERMISSIONS' | 'RATE_LIMIT_EXCEEDED'; message?: string };

/**
 * Creates a successful payment auth result
 */
export function createSuccessfulPaymentAuth(user: any = createMockUser(), tenant: any = { id: "tenant-123" }): PaymentAuthResult {
  return {
    ok: true,
    user,
    tenant,
  };
}

/**
 * Creates an unauthorized payment auth result
 */
export function createUnauthorizedPaymentAuth(message?: string): PaymentAuthResult {
  return {
    ok: false,
    code: 'UNAUTHORIZED',
    message: message || 'User not authenticated',
  };
}

/**
 * Creates an insufficient permissions payment auth result
 */
export function createInsufficientPermissionsAuth(message?: string): PaymentAuthResult {
  return {
    ok: false,
    code: 'INSUFFICIENT_PERMISSIONS',
    message: message || 'User does not have required permissions',
  };
}

/**
 * Creates a rate limited payment auth result
 */
export function createRateLimitedAuth(message?: string): PaymentAuthResult {
  return {
    ok: false,
    code: 'RATE_LIMIT_EXCEEDED',
    message: message || 'Rate limit exceeded',
  };
}

/**
 * Helper to override payment auth for a specific test
 * Usage: overridePaymentAuth(createUnauthorizedPaymentAuth())
 */
export async function overridePaymentAuth(result: PaymentAuthResult) {
  const { authorizeRequest } = await import('@/lib/auth/payment-auth');
  (authorizeRequest as unknown as vi.Mock).mockResolvedValueOnce(result);
}

/**
 * Helper to override payment auth for multiple calls
 */
export async function overridePaymentAuthMultiple(results: PaymentAuthResult[]) {
  const { authorizeRequest } = await import('@/lib/auth/payment-auth');
  const mock = authorizeRequest as unknown as vi.Mock;
  
  results.forEach(result => {
    mock.mockResolvedValueOnce(result);
  });
}

/**
 * Helper to reset payment auth mock to default successful state
 */
export async function resetPaymentAuthToSuccess(user?: any, tenant?: any) {
  const { authorizeRequest } = await import('@/lib/auth/payment-auth');
  (authorizeRequest as unknown as vi.Mock).mockResolvedValue(
    createSuccessfulPaymentAuth(user, tenant)
  );
}

/**
 * Helper to spy on user context methods
 */
export function spyOnUserContext() {
  return {
    useUser: vi.fn(() => ({
      user: createMockUser(),
      loading: false,
      error: null,
    })),
    useAuth: vi.fn(() => ({
      profile: createMockUser(),
      isAuthenticated: true,
      isLoading: false,
    })),
  };
}

/**
 * Helper to create JWT payload for testing
 */
export function createMockJWTPayload(overrides: Partial<any> = {}) {
  return {
    sub: "user-123",
    email: "<EMAIL>",
    aud: "authenticated",
    role: "authenticated",
    app_metadata: {
      tenant_id: "tenant-123",
      is_super_admin: false,
    },
    user_metadata: {},
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
    ...overrides,
  };
}

/**
 * Helper to create expired JWT payload
 */
export function createExpiredJWTPayload(overrides: Partial<any> = {}) {
  return createMockJWTPayload({
    iat: Math.floor(Date.now() / 1000) - 7200, // 2 hours ago
    exp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago (expired)
    ...overrides,
  });
}

/**
 * Helper to create malformed JWT payload
 */
export function createMalformedJWTPayload() {
  return {
    // Missing required fields
    sub: "user-123",
    // No email, aud, role, etc.
  };
}

/**
 * Helper to test role-based access control
 */
export function testRoleAccess(
  testFn: (user: any) => Promise<any> | any,
  allowedRoles: string[],
  deniedRoles: string[]
) {
  return {
    async testAllowed() {
      const results = [];
      for (const role of allowedRoles) {
        const user = createMockUser({ role });
        const result = await testFn(user);
        results.push({ role, result, allowed: true });
      }
      return results;
    },
    
    async testDenied() {
      const results = [];
      for (const role of deniedRoles) {
        const user = createMockUser({ role });
        try {
          const result = await testFn(user);
          results.push({ role, result, allowed: true, unexpected: true });
        } catch (error) {
          results.push({ role, error, allowed: false });
        }
      }
      return results;
    },
  };
}
