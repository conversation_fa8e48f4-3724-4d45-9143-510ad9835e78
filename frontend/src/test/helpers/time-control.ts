/**
 * Time Control Helpers for Testing
 * 
 * Provides utilities for controlling time in tests, particularly useful
 * for rate limiting, caching, and time-dependent functionality testing.
 */

import { vi } from "vitest";

/**
 * Sets up fake timers with a deterministic start time
 */
export function setupFakeTimers(startTime: string = '2025-01-01T00:00:00Z') {
  vi.useFakeTimers();
  vi.setSystemTime(new Date(startTime));
}

/**
 * Cleans up fake timers after tests
 */
export function cleanupFakeTimers() {
  vi.useRealTimers();
}

/**
 * Advances time by the specified number of milliseconds
 */
export function advanceTime(milliseconds: number) {
  vi.advanceTimersByTime(milliseconds);
}

/**
 * Advances time by the specified number of seconds
 */
export function advanceTimeBySeconds(seconds: number) {
  advanceTime(seconds * 1000);
}

/**
 * Advances time by the specified number of minutes
 */
export function advanceTimeByMinutes(minutes: number) {
  advanceTime(minutes * 60 * 1000);
}

/**
 * Advances time by the specified number of hours
 */
export function advanceTimeByHours(hours: number) {
  advanceTime(hours * 60 * 60 * 1000);
}

/**
 * Sets the system time to a specific date/time
 */
export function setSystemTime(dateTime: string | Date) {
  vi.setSystemTime(new Date(dateTime));
}

/**
 * Gets the current mocked time as a Date object
 */
export function getCurrentTime(): Date {
  return new Date();
}

/**
 * Helper for rate limiting tests - simulates multiple requests over time
 */
export function simulateRequestsOverTime(
  requestFn: () => Promise<any>,
  requestCount: number,
  intervalMs: number
): Promise<any[]> {
  const results: Promise<any>[] = [];
  
  for (let i = 0; i < requestCount; i++) {
    if (i > 0) {
      advanceTime(intervalMs);
    }
    results.push(requestFn());
  }
  
  return Promise.all(results);
}

/**
 * Helper for testing time-based expiration
 */
export function testExpiration(
  setupFn: () => void,
  checkFn: () => boolean,
  expirationTimeMs: number
): { beforeExpiration: boolean; afterExpiration: boolean } {
  setupFn();
  
  // Check before expiration
  advanceTime(expirationTimeMs - 1);
  const beforeExpiration = checkFn();
  
  // Check after expiration
  advanceTime(2);
  const afterExpiration = checkFn();
  
  return { beforeExpiration, afterExpiration };
}

/**
 * Helper for testing sliding window rate limits
 */
export function testSlidingWindow(
  requestFn: () => Promise<any>,
  windowSizeMs: number,
  maxRequests: number
): Promise<{
  initialRequests: any[];
  afterWindowSlide: any[];
  exceedsLimit: any;
}> {
  return new Promise(async (resolve) => {
    // Make initial requests up to the limit
    const initialRequests = [];
    for (let i = 0; i < maxRequests; i++) {
      initialRequests.push(await requestFn());
    }
    
    // Try to exceed the limit (should fail)
    let exceedsLimit;
    try {
      exceedsLimit = await requestFn();
    } catch (error) {
      exceedsLimit = error;
    }
    
    // Advance time to slide the window
    advanceTime(windowSizeMs + 1);
    
    // Should be able to make requests again
    const afterWindowSlide = [];
    for (let i = 0; i < maxRequests; i++) {
      afterWindowSlide.push(await requestFn());
    }
    
    resolve({
      initialRequests,
      afterWindowSlide,
      exceedsLimit,
    });
  });
}

/**
 * Helper for testing fixed window rate limits
 */
export function testFixedWindow(
  requestFn: () => Promise<any>,
  windowSizeMs: number,
  maxRequests: number
): Promise<{
  withinWindow: any[];
  exceedsLimit: any;
  nextWindow: any[];
}> {
  return new Promise(async (resolve) => {
    // Make requests within the window
    const withinWindow = [];
    for (let i = 0; i < maxRequests; i++) {
      withinWindow.push(await requestFn());
    }
    
    // Try to exceed the limit
    let exceedsLimit;
    try {
      exceedsLimit = await requestFn();
    } catch (error) {
      exceedsLimit = error;
    }
    
    // Advance to next window
    advanceTime(windowSizeMs);
    
    // Should be able to make requests in new window
    const nextWindow = [];
    for (let i = 0; i < maxRequests; i++) {
      nextWindow.push(await requestFn());
    }
    
    resolve({
      withinWindow,
      exceedsLimit,
      nextWindow,
    });
  });
}

/**
 * Helper for testing cache TTL (Time To Live)
 */
export function testCacheTTL(
  setCacheFn: (key: string, value: any, ttlMs: number) => void,
  getCacheFn: (key: string) => any,
  key: string,
  value: any,
  ttlMs: number
): { beforeExpiry: any; afterExpiry: any } {
  setCacheFn(key, value, ttlMs);
  
  // Check before expiry
  advanceTime(ttlMs - 1);
  const beforeExpiry = getCacheFn(key);
  
  // Check after expiry
  advanceTime(2);
  const afterExpiry = getCacheFn(key);
  
  return { beforeExpiry, afterExpiry };
}

/**
 * Time constants for common test scenarios
 */
export const TIME_CONSTANTS = {
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
} as const;
