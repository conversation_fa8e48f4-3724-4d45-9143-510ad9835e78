/**
 * Neo4j Mock Factory for Testing
 * 
 * Provides mock implementations for Neo4j driver, session, and query operations
 * to enable testing of graph service functionality without a real database.
 */

import { vi } from "vitest";

export interface MockNeo4jRecord {
  get: vi.MockedFunction<(key: string) => any>;
  keys: string[];
  length: number;
  _fields: any[];
  _fieldLookup: Record<string, number>;
}

export interface MockNeo4jResult {
  records: MockNeo4jRecord[];
  summary: {
    query: {
      text: string;
      parameters: Record<string, any>;
    };
    queryType: string;
    counters: {
      _stats: Record<string, number>;
      updates: () => boolean;
    };
  };
}

export interface MockNeo4jSession {
  run: vi.MockedFunction<(query: string, params?: Record<string, any>) => Promise<MockNeo4jResult>>;
  close: vi.MockedFunction<() => Promise<void>>;
  beginTransaction: vi.MockedFunction<() => any>;
  readTransaction: vi.MockedFunction<(fn: Function) => Promise<any>>;
  writeTransaction: vi.MockedFunction<(fn: Function) => Promise<any>>;
}

export interface MockNeo4jDriver {
  session: vi.MockedFunction<(config?: any) => MockNeo4jSession>;
  verifyConnectivity: vi.MockedFunction<() => Promise<void>>;
  close: vi.MockedFunction<() => Promise<void>>;
  getServerInfo: vi.MockedFunction<() => Promise<any>>;
}

/**
 * Creates a mock Neo4j record with the specified data
 */
export function createMockRecord(data: Record<string, any>): MockNeo4jRecord {
  const keys = Object.keys(data);
  const fields = Object.values(data);
  const fieldLookup = keys.reduce((lookup, key, index) => {
    lookup[key] = index;
    return lookup;
  }, {} as Record<string, number>);

  return {
    get: vi.fn((key: string) => data[key]),
    keys,
    length: keys.length,
    _fields: fields,
    _fieldLookup: fieldLookup,
  };
}

/**
 * Creates a mock Neo4j query result with the specified records
 */
export function createMockResult(
  records: MockNeo4jRecord[] = [],
  query: string = "MATCH (n) RETURN n",
  parameters: Record<string, any> = {}
): MockNeo4jResult {
  return {
    records,
    summary: {
      query: {
        text: query,
        parameters,
      },
      queryType: "r",
      counters: {
        _stats: {},
        updates: () => false,
      },
    },
  };
}

/**
 * Creates a mock Neo4j session with configurable behavior
 */
export function createMockSession(): MockNeo4jSession {
  const session: MockNeo4jSession = {
    run: vi.fn().mockResolvedValue(createMockResult()),
    close: vi.fn().mockResolvedValue(undefined),
    beginTransaction: vi.fn(),
    readTransaction: vi.fn(),
    writeTransaction: vi.fn(),
  };

  // Configure transaction methods to execute the provided function
  session.readTransaction.mockImplementation(async (fn: Function) => {
    return await fn(session);
  });

  session.writeTransaction.mockImplementation(async (fn: Function) => {
    return await fn(session);
  });

  return session;
}

/**
 * Creates a complete Neo4j driver mock with all necessary methods
 */
export function makeNeo4jMock(): MockNeo4jDriver {
  const mockSession = createMockSession();

  return {
    session: vi.fn(() => mockSession),
    verifyConnectivity: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined),
    getServerInfo: vi.fn().mockResolvedValue({
      address: "localhost:7687",
      version: "4.4.0",
    }),
  };
}

/**
 * Helper to create person node data for graph tests
 */
export function createPersonNode(id: string, name: string, additionalProps: Record<string, any> = {}) {
  return {
    identity: { low: parseInt(id), high: 0 },
    labels: ["Person"],
    properties: {
      id,
      name,
      ...additionalProps,
    },
  };
}

/**
 * Helper to create case node data for graph tests
 */
export function createCaseNode(id: string, title: string, additionalProps: Record<string, any> = {}) {
  return {
    identity: { low: parseInt(id), high: 0 },
    labels: ["Case"],
    properties: {
      id,
      title,
      ...additionalProps,
    },
  };
}

/**
 * Helper to create relationship data for graph tests
 */
export function createRelationship(
  id: string,
  type: string,
  startNodeId: string,
  endNodeId: string,
  properties: Record<string, any> = {}
) {
  return {
    identity: { low: parseInt(id), high: 0 },
    start: { low: parseInt(startNodeId), high: 0 },
    end: { low: parseInt(endNodeId), high: 0 },
    type,
    properties,
  };
}

/**
 * Configures a mock session to return specific graph data
 */
export function configureMockSessionForGraph(
  session: MockNeo4jSession,
  nodes: any[],
  relationships: any[] = []
) {
  const records = nodes.map(node => createMockRecord({ n: node }));
  if (relationships.length > 0) {
    relationships.forEach(rel => {
      records.push(createMockRecord({ r: rel }));
    });
  }

  session.run.mockResolvedValue(createMockResult(records));
  return session;
}
