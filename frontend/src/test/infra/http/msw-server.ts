/**
 * <PERSON><PERSON> (Mock Service Worker) Server for Integration Tests
 * 
 * Provides HTTP request interception for testing API interactions
 * without hitting real backend services.
 */

import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';

/**
 * Default API handlers for common endpoints
 */
export const defaultHandlers = [
  // Authentication endpoints
  http.post('/api/auth/login', () => {
    return HttpResponse.json({
      user: {
        id: 'test-user-1',
        email: '<EMAIL>',
        role: 'attorney',
        tenantId: 'test-tenant-1'
      },
      token: 'mock-jwt-token',
      refreshToken: 'mock-refresh-token'
    });
  }),

  http.post('/api/auth/logout', () => {
    return HttpResponse.json({ success: true });
  }),

  http.get('/api/auth/me', () => {
    return HttpResponse.json({
      id: 'test-user-1',
      email: '<EMAIL>',
      role: 'attorney',
      tenantId: 'test-tenant-1',
      permissions: ['read:documents', 'write:documents', 'read:cases']
    });
  }),

  // Medical endpoints
  http.post('/api/medical/analyze', () => {
    return HttpResponse.json({
      analysis: {
        entities: [
          { type: 'PERSON_NAME', value: 'John Doe', confidence: 0.95 },
          { type: 'DATE_OF_BIRTH', value: '01/15/1980', confidence: 0.9 }
        ],
        summary: 'Medical record analysis completed successfully.',
        riskLevel: 'medium'
      },
      processingTime: 1250
    });
  }),

  http.post('/api/medical/phi-detection', () => {
    return HttpResponse.json({
      phiDetected: true,
      entities: [
        { type: 'SSN', value: '***-**-****', confidence: 0.98 },
        { type: 'PHONE_NUMBER', value: '(***) ***-****', confidence: 0.92 }
      ],
      confidenceScore: 0.95,
      processingTime: 850
    });
  }),

  http.post('/api/medical/copilot/chat', () => {
    return HttpResponse.json({
      response: 'Based on the medical record analysis, I recommend reviewing the patient\'s current medications and considering additional diagnostic tests.',
      confidence: 0.88,
      sources: ['medical-record-1', 'clinical-guidelines-2'],
      metadata: {
        model: 'medical-copilot-v1',
        processingTime: 2100
      }
    });
  }),

  // Document endpoints
  http.get('/api/documents', () => {
    return HttpResponse.json({
      documents: [
        {
          id: 'doc-1',
          name: 'Medical Record - Patient A',
          type: 'medical_record',
          uploadedAt: '2024-03-15T10:00:00Z',
          size: 1024000,
          phiDetected: true
        },
        {
          id: 'doc-2',
          name: 'Legal Brief - Case B',
          type: 'legal_document',
          uploadedAt: '2024-03-15T11:00:00Z',
          size: 512000,
          phiDetected: false
        }
      ],
      total: 2,
      page: 1,
      limit: 10
    });
  }),

  http.post('/api/documents/upload', () => {
    return HttpResponse.json({
      document: {
        id: 'doc-new',
        name: 'uploaded-document.pdf',
        type: 'unknown',
        uploadedAt: new Date().toISOString(),
        size: 2048000,
        status: 'processing'
      }
    });
  }),

  // Activity/Insights endpoints
  http.get('/api/activity/insights', () => {
    return HttpResponse.json({
      insights: [
        {
          type: 'document_analysis',
          title: 'New Medical Records Processed',
          description: '5 medical records analyzed with PHI detection',
          timestamp: '2024-03-15T12:00:00Z',
          priority: 'medium'
        },
        {
          type: 'compliance_alert',
          title: 'HIPAA Audit Required',
          description: 'Monthly HIPAA compliance audit due',
          timestamp: '2024-03-15T11:30:00Z',
          priority: 'high'
        }
      ],
      summary: {
        totalDocuments: 25,
        phiDetected: 12,
        complianceScore: 0.92
      }
    });
  }),

  // Subscription endpoints
  http.get('/api/subscription/plans', () => {
    return HttpResponse.json({
      plans: [
        {
          id: 'solo',
          name: 'Solo Practice',
          price: 99,
          currency: 'USD',
          features: ['Basic PHI Detection', 'Document Analysis']
        },
        {
          id: 'team',
          name: 'Team Practice',
          price: 299,
          currency: 'USD',
          features: ['Advanced PHI Detection', 'Medical Copilot', 'Team Collaboration']
        }
      ]
    });
  }),

  // Error simulation endpoints
  http.get('/api/error/500', () => {
    return HttpResponse.json(
      { error: 'Internal Server Error', code: 'INTERNAL_ERROR' },
      { status: 500 }
    );
  }),

  http.get('/api/error/401', () => {
    return HttpResponse.json(
      { error: 'Unauthorized', code: 'AUTH_REQUIRED' },
      { status: 401 }
    );
  }),

  http.get('/api/error/403', () => {
    return HttpResponse.json(
      { error: 'Forbidden', code: 'INSUFFICIENT_PERMISSIONS' },
      { status: 403 }
    );
  }),

  // Slow response simulation
  http.get('/api/slow', async () => {
    await new Promise(resolve => setTimeout(resolve, 3000));
    return HttpResponse.json({ message: 'Slow response completed' });
  })
];

/**
 * MSW server instance
 */
export const server = setupServer(...defaultHandlers);

/**
 * Medical-specific handlers for medical copilot testing
 */
export const medicalHandlers = [
  http.post('/api/medical/copilot/session', () => {
    return HttpResponse.json({
      sessionId: 'medical-session-1',
      config: {
        enableStreaming: true,
        enablePHIDetection: true,
        maxHistoryLength: 50
      }
    });
  }),

  http.post('/api/medical/copilot/stream', () => {
    // Simulate streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        const chunks = [
          'Based on the medical record, ',
          'the patient presents with symptoms ',
          'consistent with acute myocardial infarction. ',
          'Immediate cardiac catheterization is recommended.'
        ];
        
        chunks.forEach((chunk, index) => {
          setTimeout(() => {
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ content: chunk })}\n\n`));
            if (index === chunks.length - 1) {
              controller.close();
            }
          }, index * 500);
        });
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    });
  })
];

/**
 * Compliance-specific handlers for HIPAA testing
 */
export const complianceHandlers = [
  http.post('/api/compliance/audit', () => {
    return HttpResponse.json({
      auditId: 'audit-1',
      timestamp: new Date().toISOString(),
      status: 'logged'
    });
  }),

  http.get('/api/compliance/audit/:auditId', ({ params }) => {
    return HttpResponse.json({
      id: params.auditId,
      eventType: 'PHI_ACCESS',
      userId: 'test-user-1',
      timestamp: '2024-03-15T10:00:00Z',
      metadata: {
        documentId: 'doc-1',
        accessType: 'view'
      }
    });
  }),

  http.post('/api/compliance/phi-scan', () => {
    return HttpResponse.json({
      scanId: 'scan-1',
      phiFound: true,
      entities: [
        { type: 'SSN', confidence: 0.95, redacted: true },
        { type: 'PHONE_NUMBER', confidence: 0.88, redacted: true }
      ],
      riskLevel: 'high'
    });
  })
];

/**
 * Test utilities for MSW server management
 */
export const MSWTestUtils = {
  /**
   * Start the server with default handlers
   */
  start: () => {
    server.listen({ onUnhandledRequest: 'warn' });
  },

  /**
   * Stop the server
   */
  stop: () => {
    server.close();
  },

  /**
   * Reset handlers to default state
   */
  reset: () => {
    server.resetHandlers();
  },

  /**
   * Add custom handlers for specific tests
   */
  addHandlers: (handlers: any[]) => {
    server.use(...handlers);
  },

  /**
   * Mock a successful API response
   */
  mockSuccess: (endpoint: string, data: any) => {
    server.use(
      http.get(endpoint, () => HttpResponse.json(data)),
      http.post(endpoint, () => HttpResponse.json(data))
    );
  },

  /**
   * Mock an API error response
   */
  mockError: (endpoint: string, status: number, error: any) => {
    server.use(
      http.get(endpoint, () => HttpResponse.json(error, { status })),
      http.post(endpoint, () => HttpResponse.json(error, { status }))
    );
  },

  /**
   * Mock network timeout
   */
  mockTimeout: (endpoint: string, delay: number = 5000) => {
    server.use(
      http.get(endpoint, async () => {
        await new Promise(resolve => setTimeout(resolve, delay));
        return HttpResponse.json({ error: 'Request timeout' }, { status: 408 });
      })
    );
  },

  /**
   * Get request history (for verification in tests)
   */
  getRequestHistory: () => {
    // Note: This would need to be implemented with request tracking
    // For now, return empty array
    return [];
  }
};

/**
 * Preset server configurations for different test scenarios
 */
export const ServerPresets = {
  /**
   * Medical copilot testing preset
   */
  medicalCopilot: () => {
    server.use(...medicalHandlers);
  },

  /**
   * HIPAA compliance testing preset
   */
  hipaaCompliance: () => {
    server.use(...complianceHandlers);
  },

  /**
   * Error scenarios preset
   */
  errorScenarios: () => {
    server.use(
      http.get('/api/*', () => 
        HttpResponse.json({ error: 'Service unavailable' }, { status: 503 })
      )
    );
  },

  /**
   * Slow network preset
   */
  slowNetwork: () => {
    server.use(
      http.get('/api/*', async () => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        return HttpResponse.json({ message: 'Slow response' });
      })
    );
  }
};
