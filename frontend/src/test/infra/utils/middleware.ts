/**
 * Middleware Testing Utilities
 * 
 * Provides utilities for testing Next.js middleware and API routes
 * with proper request/response mocking.
 */

import { NextRequest, NextResponse } from 'next/server';

export interface MockRequestOptions {
  url?: string;
  method?: string;
  headers?: Record<string, string>;
  body?: any;
  cookies?: Record<string, string>;
  searchParams?: Record<string, string>;
  ip?: string;
  geo?: {
    country?: string;
    region?: string;
    city?: string;
  };
}

export interface MockResponseCapture {
  status?: number;
  headers: Record<string, string>;
  body?: any;
  cookies: Array<{
    name: string;
    value: string;
    options?: any;
  }>;
  redirected?: boolean;
  redirectUrl?: string;
}

/**
 * Create a mock NextRequest for testing
 */
export function createMockRequest(options: MockRequestOptions = {}): NextRequest {
  const {
    url = 'http://localhost:3000',
    method = 'GET',
    headers = {},
    body,
    cookies = {},
    searchParams = {},
    ip = '127.0.0.1',
    geo = {}
  } = options;

  // Build URL with search params
  const urlObj = new URL(url);
  Object.entries(searchParams).forEach(([key, value]) => {
    urlObj.searchParams.set(key, value);
  });

  // Create headers
  const requestHeaders = new Headers();
  Object.entries(headers).forEach(([key, value]) => {
    requestHeaders.set(key, value);
  });

  // Add cookies to headers
  if (Object.keys(cookies).length > 0) {
    const cookieString = Object.entries(cookies)
      .map(([name, value]) => `${name}=${value}`)
      .join('; ');
    requestHeaders.set('cookie', cookieString);
  }

  // Create request init
  const requestInit: RequestInit = {
    method,
    headers: requestHeaders
  };

  if (body && method !== 'GET' && method !== 'HEAD') {
    if (typeof body === 'object') {
      requestInit.body = JSON.stringify(body);
      requestHeaders.set('content-type', 'application/json');
    } else {
      requestInit.body = body;
    }
  }

  // Create the request
  const request = new NextRequest(urlObj.toString(), requestInit);

  // Mock additional properties
  Object.defineProperty(request, 'ip', {
    value: ip,
    writable: false
  });

  Object.defineProperty(request, 'geo', {
    value: geo,
    writable: false
  });

  return request;
}

/**
 * Capture and analyze NextResponse for testing
 */
export function captureResponse(response: NextResponse): MockResponseCapture {
  const capture: MockResponseCapture = {
    status: response.status,
    headers: {},
    cookies: []
  };

  // Capture headers
  response.headers.forEach((value, key) => {
    capture.headers[key] = value;
  });

  // Capture cookies
  const setCookieHeaders = response.headers.getSetCookie?.() || [];
  setCookieHeaders.forEach(cookieHeader => {
    const [nameValue, ...options] = cookieHeader.split(';');
    const [name, value] = nameValue.split('=');
    
    capture.cookies.push({
      name: name.trim(),
      value: value?.trim() || '',
      options: options.map(opt => opt.trim())
    });
  });

  // Check for redirects
  if (response.status >= 300 && response.status < 400) {
    capture.redirected = true;
    capture.redirectUrl = response.headers.get('location') || undefined;
  }

  return capture;
}

/**
 * Middleware test runner utility
 */
export class MiddlewareTestRunner {
  private middleware: (request: NextRequest) => Promise<NextResponse> | NextResponse;

  constructor(middleware: (request: NextRequest) => Promise<NextResponse> | NextResponse) {
    this.middleware = middleware;
  }

  async run(requestOptions: MockRequestOptions = {}): Promise<{
    request: NextRequest;
    response: NextResponse;
    capture: MockResponseCapture;
  }> {
    const request = createMockRequest(requestOptions);
    const response = await this.middleware(request);
    const capture = captureResponse(response);

    return { request, response, capture };
  }

  async expectStatus(requestOptions: MockRequestOptions, expectedStatus: number): Promise<void> {
    const { capture } = await this.run(requestOptions);
    if (capture.status !== expectedStatus) {
      throw new Error(`Expected status ${expectedStatus}, got ${capture.status}`);
    }
  }

  async expectRedirect(requestOptions: MockRequestOptions, expectedUrl?: string): Promise<void> {
    const { capture } = await this.run(requestOptions);
    if (!capture.redirected) {
      throw new Error('Expected redirect, but response was not a redirect');
    }
    if (expectedUrl && capture.redirectUrl !== expectedUrl) {
      throw new Error(`Expected redirect to ${expectedUrl}, got ${capture.redirectUrl}`);
    }
  }

  async expectHeader(
    requestOptions: MockRequestOptions,
    headerName: string,
    expectedValue?: string
  ): Promise<void> {
    const { capture } = await this.run(requestOptions);
    const actualValue = capture.headers[headerName.toLowerCase()];
    
    if (expectedValue === undefined) {
      if (actualValue === undefined) {
        throw new Error(`Expected header ${headerName} to be present`);
      }
    } else if (actualValue !== expectedValue) {
      throw new Error(`Expected header ${headerName} to be ${expectedValue}, got ${actualValue}`);
    }
  }

  async expectCookie(
    requestOptions: MockRequestOptions,
    cookieName: string,
    expectedValue?: string
  ): Promise<void> {
    const { capture } = await this.run(requestOptions);
    const cookie = capture.cookies.find(c => c.name === cookieName);
    
    if (!cookie) {
      throw new Error(`Expected cookie ${cookieName} to be set`);
    }
    
    if (expectedValue !== undefined && cookie.value !== expectedValue) {
      throw new Error(`Expected cookie ${cookieName} to be ${expectedValue}, got ${cookie.value}`);
    }
  }
}

/**
 * API Route testing utilities
 */
export interface ApiRouteTestOptions {
  params?: Record<string, string>;
  searchParams?: Record<string, string>;
  body?: any;
  headers?: Record<string, string>;
  method?: string;
}

export function createApiRequest(
  path: string,
  options: ApiRouteTestOptions = {}
): NextRequest {
  const {
    params = {},
    searchParams = {},
    body,
    headers = {},
    method = 'GET'
  } = options;

  // Build URL with path and search params
  let url = `http://localhost:3000${path}`;
  
  // Replace path parameters
  Object.entries(params).forEach(([key, value]) => {
    url = url.replace(`[${key}]`, value);
  });

  return createMockRequest({
    url,
    method,
    headers,
    body,
    searchParams
  });
}

/**
 * Authentication testing utilities
 */
export const AuthTestUtils = {
  /**
   * Create request with JWT token
   */
  withJwtToken: (token: string): Record<string, string> => ({
    authorization: `Bearer ${token}`
  }),

  /**
   * Create request with session cookie
   */
  withSessionCookie: (sessionId: string): Record<string, string> => ({
    cookie: `session=${sessionId}`
  }),

  /**
   * Create mock JWT token for testing
   */
  createMockJwt: (payload: Record<string, any> = {}): string => {
    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
    const body = btoa(JSON.stringify({
      sub: 'test-user',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
      ...payload
    }));
    const signature = 'mock-signature';
    
    return `${header}.${body}.${signature}`;
  },

  /**
   * Create expired JWT token
   */
  createExpiredJwt: (): string => {
    return AuthTestUtils.createMockJwt({
      exp: Math.floor(Date.now() / 1000) - 3600 // Expired 1 hour ago
    });
  }
};

/**
 * CORS testing utilities
 */
export const CorsTestUtils = {
  /**
   * Create preflight OPTIONS request
   */
  createPreflightRequest: (origin: string, method: string): NextRequest => {
    return createMockRequest({
      method: 'OPTIONS',
      headers: {
        'origin': origin,
        'access-control-request-method': method,
        'access-control-request-headers': 'content-type,authorization'
      }
    });
  },

  /**
   * Verify CORS headers in response
   */
  verifyCorsHeaders: (
    capture: MockResponseCapture,
    expectedOrigin?: string,
    expectedMethods?: string[]
  ): void => {
    const allowOrigin = capture.headers['access-control-allow-origin'];
    const allowMethods = capture.headers['access-control-allow-methods'];
    
    if (expectedOrigin && allowOrigin !== expectedOrigin) {
      throw new Error(`Expected CORS origin ${expectedOrigin}, got ${allowOrigin}`);
    }
    
    if (expectedMethods) {
      const actualMethods = allowMethods?.split(',').map(m => m.trim()) || [];
      const missingMethods = expectedMethods.filter(m => !actualMethods.includes(m));
      
      if (missingMethods.length > 0) {
        throw new Error(`Missing CORS methods: ${missingMethods.join(', ')}`);
      }
    }
  }
};

/**
 * Rate limiting testing utilities
 */
export const RateLimitTestUtils = {
  /**
   * Create multiple requests from same IP
   */
  createBurstRequests: (
    count: number,
    ip: string = '127.0.0.1',
    baseOptions: MockRequestOptions = {}
  ): NextRequest[] => {
    return Array.from({ length: count }, () => 
      createMockRequest({ ...baseOptions, ip })
    );
  },

  /**
   * Verify rate limit headers
   */
  verifyRateLimitHeaders: (capture: MockResponseCapture): {
    limit?: number;
    remaining?: number;
    reset?: number;
  } => {
    return {
      limit: capture.headers['x-ratelimit-limit'] ? 
        parseInt(capture.headers['x-ratelimit-limit']) : undefined,
      remaining: capture.headers['x-ratelimit-remaining'] ? 
        parseInt(capture.headers['x-ratelimit-remaining']) : undefined,
      reset: capture.headers['x-ratelimit-reset'] ? 
        parseInt(capture.headers['x-ratelimit-reset']) : undefined
    };
  }
};
