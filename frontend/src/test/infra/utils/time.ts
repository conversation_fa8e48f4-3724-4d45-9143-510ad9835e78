/**
 * Time Testing Utilities
 * 
 * Provides utilities for controlling time in tests using vi.useFakeTimers
 * and other time-related testing helpers.
 */

import { vi } from 'vitest';

export interface TimeTestOptions {
  now?: Date | string | number;
  shouldAdvanceTime?: boolean;
  toFake?: Array<'setTimeout' | 'clearTimeout' | 'setInterval' | 'clearInterval' | 'Date'>;
}

/**
 * Time control utilities for testing
 */
export class TimeController {
  private isUsingFakeTimers = false;
  private originalNow: number;

  constructor() {
    this.originalNow = Date.now();
  }

  /**
   * Start using fake timers
   */
  useFakeTimers(options: TimeTestOptions = {}): void {
    const {
      now = new Date('2024-03-15T10:00:00Z'),
      shouldAdvanceTime = false,
      toFake = ['setTimeout', 'clearTimeout', 'setInterval', 'clearInterval', 'Date']
    } = options;

    vi.useFakeTimers({
      shouldAdvanceTime,
      toFake
    });

    this.setSystemTime(now);
    this.isUsingFakeTimers = true;
  }

  /**
   * Restore real timers
   */
  useRealTimers(): void {
    if (this.isUsingFakeTimers) {
      vi.useRealTimers();
      this.isUsingFakeTimers = false;
    }
  }

  /**
   * Set the system time
   */
  setSystemTime(time: Date | string | number): void {
    vi.setSystemTime(time);
  }

  /**
   * Advance time by specified amount
   */
  advanceTimersByTime(ms: number): void {
    if (!this.isUsingFakeTimers) {
      throw new Error('Must call useFakeTimers() before advancing time');
    }
    vi.advanceTimersByTime(ms);
  }

  /**
   * Advance time to next timer
   */
  advanceTimersToNextTimer(): void {
    if (!this.isUsingFakeTimers) {
      throw new Error('Must call useFakeTimers() before advancing timers');
    }
    vi.advanceTimersToNextTimer();
  }

  /**
   * Run all pending timers
   */
  runAllTimers(): void {
    if (!this.isUsingFakeTimers) {
      throw new Error('Must call useFakeTimers() before running timers');
    }
    vi.runAllTimers();
  }

  /**
   * Run only currently pending timers
   */
  runOnlyPendingTimers(): void {
    if (!this.isUsingFakeTimers) {
      throw new Error('Must call useFakeTimers() before running timers');
    }
    vi.runOnlyPendingTimers();
  }

  /**
   * Get number of pending timers
   */
  getTimerCount(): number {
    return vi.getTimerCount();
  }

  /**
   * Clear all timers
   */
  clearAllTimers(): void {
    vi.clearAllTimers();
  }

  /**
   * Check if using fake timers
   */
  isFake(): boolean {
    return this.isUsingFakeTimers;
  }
}

/**
 * Date/Time testing utilities
 */
export const DateTimeTestUtils = {
  /**
   * Create a fixed date for testing
   */
  createFixedDate: (dateString: string = '2024-03-15T10:00:00Z'): Date => {
    return new Date(dateString);
  },

  /**
   * Create date relative to now
   */
  createRelativeDate: (offsetMs: number): Date => {
    return new Date(Date.now() + offsetMs);
  },

  /**
   * Time constants for readability
   */
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,

  /**
   * Format duration for test descriptions
   */
  formatDuration: (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${ms / 1000}s`;
    if (ms < 3600000) return `${ms / 60000}m`;
    if (ms < 86400000) return `${ms / 3600000}h`;
    return `${ms / 86400000}d`;
  },

  /**
   * Check if date is within range
   */
  isWithinRange: (date: Date, start: Date, end: Date): boolean => {
    return date >= start && date <= end;
  },

  /**
   * Check if date is recent (within last N milliseconds)
   */
  isRecent: (date: Date, withinMs: number = 5000): boolean => {
    const now = new Date();
    return (now.getTime() - date.getTime()) <= withinMs;
  }
};

/**
 * Timeout and interval testing utilities
 */
export const TimerTestUtils = {
  /**
   * Test setTimeout behavior
   */
  testTimeout: async (
    callback: () => void,
    delay: number,
    timeController: TimeController
  ): Promise<void> => {
    let called = false;
    
    setTimeout(() => {
      called = true;
      callback();
    }, delay);

    // Verify not called immediately
    if (called) {
      throw new Error('Timeout callback called immediately');
    }

    // Advance time and verify call
    timeController.advanceTimersByTime(delay);
    
    if (!called) {
      throw new Error('Timeout callback not called after advancing time');
    }
  },

  /**
   * Test setInterval behavior
   */
  testInterval: async (
    callback: () => void,
    interval: number,
    expectedCalls: number,
    timeController: TimeController
  ): Promise<void> => {
    let callCount = 0;
    
    const intervalId = setInterval(() => {
      callCount++;
      callback();
    }, interval);

    // Advance time for expected number of calls
    timeController.advanceTimersByTime(interval * expectedCalls);
    
    clearInterval(intervalId);

    if (callCount !== expectedCalls) {
      throw new Error(`Expected ${expectedCalls} calls, got ${callCount}`);
    }
  },

  /**
   * Test debounce behavior
   */
  testDebounce: async (
    debouncedFn: (...args: any[]) => void,
    delay: number,
    timeController: TimeController
  ): Promise<void> => {
    let callCount = 0;
    const mockFn = vi.fn(() => callCount++);
    
    // Call multiple times rapidly
    debouncedFn();
    debouncedFn();
    debouncedFn();

    // Should not be called yet
    if (callCount > 0) {
      throw new Error('Debounced function called too early');
    }

    // Advance time and verify single call
    timeController.advanceTimersByTime(delay);
    
    if (callCount !== 1) {
      throw new Error(`Expected 1 debounced call, got ${callCount}`);
    }
  },

  /**
   * Test throttle behavior
   */
  testThrottle: async (
    throttledFn: (...args: any[]) => void,
    interval: number,
    timeController: TimeController
  ): Promise<void> => {
    let callCount = 0;
    const mockFn = vi.fn(() => callCount++);
    
    // Call multiple times
    throttledFn(); // Should execute immediately
    throttledFn(); // Should be throttled
    throttledFn(); // Should be throttled

    if (callCount !== 1) {
      throw new Error(`Expected 1 immediate call, got ${callCount}`);
    }

    // Advance time past throttle interval
    timeController.advanceTimersByTime(interval + 1);
    
    throttledFn(); // Should execute now
    
    if (callCount !== 2) {
      throw new Error(`Expected 2 total calls, got ${callCount}`);
    }
  }
};

/**
 * Performance timing utilities
 */
export const PerformanceTestUtils = {
  /**
   * Measure execution time
   */
  measureTime: async <T>(fn: () => Promise<T> | T): Promise<{ result: T; duration: number }> => {
    const start = performance.now();
    const result = await fn();
    const end = performance.now();
    
    return {
      result,
      duration: end - start
    };
  },

  /**
   * Assert execution time is within bounds
   */
  assertExecutionTime: async <T>(
    fn: () => Promise<T> | T,
    maxMs: number,
    minMs: number = 0
  ): Promise<T> => {
    const { result, duration } = await PerformanceTestUtils.measureTime(fn);
    
    if (duration < minMs) {
      throw new Error(`Execution too fast: ${duration}ms < ${minMs}ms`);
    }
    
    if (duration > maxMs) {
      throw new Error(`Execution too slow: ${duration}ms > ${maxMs}ms`);
    }
    
    return result;
  },

  /**
   * Create performance benchmark
   */
  benchmark: async <T>(
    name: string,
    fn: () => Promise<T> | T,
    iterations: number = 100
  ): Promise<{
    name: string;
    iterations: number;
    totalTime: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
  }> => {
    const times: number[] = [];
    
    for (let i = 0; i < iterations; i++) {
      const { duration } = await PerformanceTestUtils.measureTime(fn);
      times.push(duration);
    }
    
    const totalTime = times.reduce((sum, time) => sum + time, 0);
    const averageTime = totalTime / iterations;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    return {
      name,
      iterations,
      totalTime,
      averageTime,
      minTime,
      maxTime
    };
  }
};

/**
 * Global time controller instance for convenience
 */
export const timeController = new TimeController();

/**
 * Convenience functions for common time operations
 */
export const TimeTestHelpers = {
  /**
   * Setup fake timers for a test
   */
  setupFakeTimers: (options?: TimeTestOptions) => {
    timeController.useFakeTimers(options);
  },

  /**
   * Cleanup fake timers after a test
   */
  cleanupFakeTimers: () => {
    timeController.useRealTimers();
  },

  /**
   * Advance time by duration
   */
  advanceTime: (ms: number) => {
    timeController.advanceTimersByTime(ms);
  },

  /**
   * Run all pending timers
   */
  runAllTimers: () => {
    timeController.runAllTimers();
  },

  /**
   * Set fixed time for test
   */
  setTime: (time: Date | string | number) => {
    timeController.setSystemTime(time);
  }
};
