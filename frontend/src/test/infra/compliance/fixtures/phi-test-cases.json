{"testCases": {"highRiskPhi": {"description": "Text containing multiple high-confidence PHI elements", "text": "<PERSON><PERSON> <PERSON> (SSN: ***********) was born on 01/15/1980. Contact at (************* or <EMAIL>. MRN: MRN123456.", "expectedEntities": [{"type": "PERSON_NAME", "value": "<PERSON>", "confidence": 0.9}, {"type": "SSN", "value": "***********", "confidence": 0.95}, {"type": "DATE_OF_BIRTH", "value": "01/15/1980", "confidence": 0.8}, {"type": "PHONE_NUMBER", "value": "(*************", "confidence": 0.9}, {"type": "EMAIL", "value": "<EMAIL>", "confidence": 0.95}, {"type": "MEDICAL_RECORD_NUMBER", "value": "MRN123456", "confidence": 0.9}], "riskLevel": "high"}, "mediumRiskPhi": {"description": "Text with some PHI but lower confidence", "text": "Patient visited on 03/15/2024. Address: 123 Main St, Anytown, ST 12345. Insurance policy #INS789012.", "expectedEntities": [{"type": "ADDRESS", "value": "123 Main St, Anytown, ST 12345", "confidence": 0.8}, {"type": "INSURANCE_ID", "value": "INS789012", "confidence": 0.7}], "riskLevel": "medium"}, "lowRiskPhi": {"description": "Text with minimal or no PHI", "text": "Patient reports feeling better after treatment. Vital signs stable. Continue current medication regimen.", "expectedEntities": [], "riskLevel": "low"}, "edgeCases": {"description": "Edge cases and false positives", "text": "Meeting scheduled for 12/25/2024 at 123 Conference Room. Call extension 1234 for details.", "expectedEntities": [], "riskLevel": "low", "notes": "Should not detect conference room numbers or extensions as PHI"}}, "redactionTests": {"preserveFormat": {"input": "SSN: ***********, Phone: (*************", "expectedOutput": "SSN: ***-**-****, Phone: (***) ***-****", "config": {"preserveFormat": true, "redactionChar": "*"}}, "standardRedaction": {"input": "<PERSON><PERSON> <PERSON>, DOB: 01/15/1980, SSN: ***********", "expectedOutput": "Patient [REDACTED_NAME], DOB: [REDACTED_DATE], SSN: [REDACTED_SSN]", "config": {"preserveFormat": false}}, "selectiveRedaction": {"input": "Dr. <PERSON> treated patient on 03/15/2024 at 123 Main Street", "expectedOutput": "Dr<PERSON> <PERSON> treated patient on 03/15/2024 at [REDACTED_ADDRESS]", "config": {"redactNames": false, "redactDates": false, "redactAddresses": true}}}, "complianceViolations": {"phiInLogs": {"description": "PHI accidentally logged in application logs", "violationType": "PHI_IN_LOGS", "severity": "high", "example": "User 123 accessed record for patient <PERSON> (SSN: ***********)"}, "unauthorizedAccess": {"description": "User accessing PHI without proper authorization", "violationType": "UNAUTHORIZED_ACCESS", "severity": "critical", "example": "User attempted to access patient records outside their assigned cases"}, "dataExportViolation": {"description": "PHI exported without proper safeguards", "violationType": "UNSAFE_DATA_EXPORT", "severity": "high", "example": "Medical records exported to unencrypted file"}, "retentionViolation": {"description": "PHI retained beyond required period", "violationType": "RETENTION_VIOLATION", "severity": "medium", "example": "Patient records not purged after 7-year retention period"}}, "auditScenarios": {"normalSession": {"description": "Typical user session with proper PHI handling", "events": [{"type": "USER_LOGIN", "timestamp": "2024-03-15T09:00:00Z"}, {"type": "DOCUMENT_VIEW", "timestamp": "2024-03-15T09:05:00Z", "phiTypes": ["PERSON_NAME", "DATE_OF_BIRTH"]}, {"type": "PHI_DETECTION", "timestamp": "2024-03-15T09:05:30Z", "phiCount": 5, "confidenceScore": 0.92}, {"type": "USER_LOGOUT", "timestamp": "2024-03-15T10:00:00Z"}], "expectedViolations": 0}, "suspiciousSession": {"description": "Session with potential compliance violations", "events": [{"type": "USER_LOGIN", "timestamp": "2024-03-15T02:00:00Z", "metadata": {"unusualHour": true}}, {"type": "DOCUMENT_VIEW", "timestamp": "2024-03-15T02:05:00Z", "phiTypes": ["SSN", "MEDICAL_RECORD_NUMBER"], "metadata": {"bulkAccess": true, "recordCount": 50}}, {"type": "DATA_EXPORT", "timestamp": "2024-03-15T02:10:00Z", "metadata": {"exportSize": "large", "encryption": false}}], "expectedViolations": 2}}}