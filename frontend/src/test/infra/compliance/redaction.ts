/**
 * PHI Redaction Utilities for HIPAA Compliance Testing
 * 
 * Provides PHI detection and redaction capabilities for testing compliance features.
 * Includes patterns for common PHI types and redaction strategies.
 */

export type PhiType = 
  | 'PERSON_NAME'
  | 'DATE_OF_BIRTH'
  | 'SSN'
  | 'PHONE_NUMBER'
  | 'EMAIL'
  | 'ADDRESS'
  | 'MEDICAL_RECORD_NUMBER'
  | 'INSURANCE_ID'
  | 'ACCOUNT_NUMBER'
  | 'DEVICE_ID'
  | 'IP_ADDRESS'
  | 'URL'
  | 'BIOMETRIC_ID';

export type PhiEntity = {
  type: PhiType;
  value: string;
  startIndex: number;
  endIndex: number;
  confidence: number;
  redacted?: boolean;
};

export type RedactionConfig = {
  enabled: boolean;
  redactNames: boolean;
  redactDates: boolean;
  redactAddresses: boolean;
  redactPhones: boolean;
  redactEmails: boolean;
  redactIds: boolean;
  preserveFormat: boolean;
  redactionChar: string;
};

/**
 * PHI Detection Patterns
 */
const PHI_PATTERNS: Record<PhiType, RegExp[]> = {
  PERSON_NAME: [
    /\b[A-Z][a-z]+,\s[A-Z][a-z]+\b/g, // Last, First
    /\b[A-Z][a-z]+\s[A-Z][a-z]+\b/g,  // First Last
    /\bDr\.\s[A-Z][a-z]+\b/g,          // Dr. Name
    /\bMr\.\s[A-Z][a-z]+\b/g,          // Mr. Name
    /\bMs\.\s[A-Z][a-z]+\b/g,          // Ms. Name
    /\bMrs\.\s[A-Z][a-z]+\b/g          // Mrs. Name
  ],
  DATE_OF_BIRTH: [
    /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g,    // MM/DD/YYYY
    /\b\d{1,2}-\d{1,2}-\d{4}\b/g,      // MM-DD-YYYY
    /\b\d{4}-\d{1,2}-\d{1,2}\b/g,      // YYYY-MM-DD
    /\bDOB:?\s*\d{1,2}\/\d{1,2}\/\d{4}\b/gi
  ],
  SSN: [
    /\b\d{3}-\d{2}-\d{4}\b/g,          // XXX-XX-XXXX
    /\b\d{3}\s\d{2}\s\d{4}\b/g,        // XXX XX XXXX
    /\bSSN:?\s*\d{3}-\d{2}-\d{4}\b/gi
  ],
  PHONE_NUMBER: [
    /\b(?:\+1\s?)?\(?\d{3}\)?[-\s]\d{3}[-\s]\d{4}\b/g, // US phone formats
    /\b\d{3}-\d{3}-\d{4}\b/g,
    /\b\(\d{3}\)\s\d{3}-\d{4}\b/g
  ],
  EMAIL: [
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
  ],
  ADDRESS: [
    /\b\d+\s[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)\b/gi,
    /\b\d+\s[A-Za-z\s]+,\s[A-Za-z\s]+,\s[A-Z]{2}\s\d{5}\b/g // Full address
  ],
  MEDICAL_RECORD_NUMBER: [
    /\bMRN:?\s*[A-Z0-9-]{6,}\b/gi,
    /\bMedical\s+Record\s+Number:?\s*[A-Z0-9-]{6,}\b/gi,
    /\bPatient\s+ID:?\s*[A-Z0-9-]{6,}\b/gi
  ],
  INSURANCE_ID: [
    /\bInsurance\s+ID:?\s*[A-Z0-9-]{6,}\b/gi,
    /\bPolicy\s+Number:?\s*[A-Z0-9-]{6,}\b/gi,
    /\bMember\s+ID:?\s*[A-Z0-9-]{6,}\b/gi
  ],
  ACCOUNT_NUMBER: [
    /\bAccount\s+Number:?\s*[A-Z0-9-]{6,}\b/gi,
    /\bAcct:?\s*[A-Z0-9-]{6,}\b/gi
  ],
  DEVICE_ID: [
    /\bDevice\s+ID:?\s*[A-Z0-9-]{8,}\b/gi,
    /\bSerial\s+Number:?\s*[A-Z0-9-]{8,}\b/gi
  ],
  IP_ADDRESS: [
    /\b(?:\d{1,3}\.){3}\d{1,3}\b/g
  ],
  URL: [
    /https?:\/\/[^\s]+/g
  ],
  BIOMETRIC_ID: [
    /\bFingerprint\s+ID:?\s*[A-Z0-9-]{8,}\b/gi,
    /\bBiometric\s+ID:?\s*[A-Z0-9-]{8,}\b/gi
  ]
};

/**
 * Default redaction configuration
 */
export const DEFAULT_REDACTION_CONFIG: RedactionConfig = {
  enabled: true,
  redactNames: true,
  redactDates: true,
  redactAddresses: true,
  redactPhones: true,
  redactEmails: true,
  redactIds: true,
  preserveFormat: false,
  redactionChar: '*'
};

/**
 * PHI Redaction Utility Class
 */
export class PhiRedactor {
  private config: RedactionConfig;

  constructor(config: Partial<RedactionConfig> = {}) {
    this.config = { ...DEFAULT_REDACTION_CONFIG, ...config };
  }

  /**
   * Detect PHI entities in text
   */
  detectPhi(text: string): PhiEntity[] {
    const entities: PhiEntity[] = [];

    for (const [type, patterns] of Object.entries(PHI_PATTERNS)) {
      for (const pattern of patterns) {
        let match;
        pattern.lastIndex = 0; // Reset regex state
        
        while ((match = pattern.exec(text)) !== null) {
          entities.push({
            type: type as PhiType,
            value: match[0],
            startIndex: match.index,
            endIndex: match.index + match[0].length,
            confidence: this.calculateConfidence(type as PhiType, match[0])
          });
        }
      }
    }

    // Sort by start index and remove overlaps
    return this.removeOverlaps(entities.sort((a, b) => a.startIndex - b.startIndex));
  }

  /**
   * Redact PHI in text based on configuration
   */
  redactPhi(text: string): {
    redactedText: string;
    redactionCount: number;
    redactionMap: Record<string, string>;
    entities: PhiEntity[];
  } {
    if (!this.config.enabled) {
      return {
        redactedText: text,
        redactionCount: 0,
        redactionMap: {},
        entities: []
      };
    }

    const entities = this.detectPhi(text);
    const redactionMap: Record<string, string> = {};
    let redactedText = text;
    let redactionCount = 0;

    // Process entities in reverse order to maintain indices
    const sortedEntities = entities.sort((a, b) => b.startIndex - a.startIndex);

    for (const entity of sortedEntities) {
      if (this.shouldRedactType(entity.type)) {
        const redactedValue = this.generateRedaction(entity);
        redactionMap[entity.value] = redactedValue;
        
        redactedText = redactedText.substring(0, entity.startIndex) + 
                      redactedValue + 
                      redactedText.substring(entity.endIndex);
        
        entity.redacted = true;
        redactionCount++;
      }
    }

    return {
      redactedText,
      redactionCount,
      redactionMap,
      entities
    };
  }

  /**
   * Validate that text contains no PHI
   */
  validateNoPhi(text: string): {
    isClean: boolean;
    foundPhi: PhiEntity[];
    riskLevel: 'low' | 'medium' | 'high';
  } {
    const entities = this.detectPhi(text);
    const highConfidenceEntities = entities.filter(e => e.confidence > 0.8);
    
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    if (highConfidenceEntities.length > 0) {
      riskLevel = 'high';
    } else if (entities.length > 0) {
      riskLevel = 'medium';
    }

    return {
      isClean: entities.length === 0,
      foundPhi: entities,
      riskLevel
    };
  }

  /**
   * Generate redacted placeholder for entity
   */
  private generateRedaction(entity: PhiEntity): string {
    if (this.config.preserveFormat) {
      return this.preserveFormatRedaction(entity);
    }

    const typeMap: Record<PhiType, string> = {
      PERSON_NAME: '[REDACTED_NAME]',
      DATE_OF_BIRTH: '[REDACTED_DATE]',
      SSN: '[REDACTED_SSN]',
      PHONE_NUMBER: '[REDACTED_PHONE]',
      EMAIL: '[REDACTED_EMAIL]',
      ADDRESS: '[REDACTED_ADDRESS]',
      MEDICAL_RECORD_NUMBER: '[REDACTED_MRN]',
      INSURANCE_ID: '[REDACTED_INSURANCE]',
      ACCOUNT_NUMBER: '[REDACTED_ACCOUNT]',
      DEVICE_ID: '[REDACTED_DEVICE]',
      IP_ADDRESS: '[REDACTED_IP]',
      URL: '[REDACTED_URL]',
      BIOMETRIC_ID: '[REDACTED_BIOMETRIC]'
    };

    return typeMap[entity.type] || '[REDACTED]';
  }

  /**
   * Generate format-preserving redaction
   */
  private preserveFormatRedaction(entity: PhiEntity): string {
    const char = this.config.redactionChar;
    
    switch (entity.type) {
      case 'SSN':
        return `${char}${char}${char}-${char}${char}-${char}${char}${char}${char}`;
      case 'PHONE_NUMBER':
        if (entity.value.includes('(')) {
          return `(${char}${char}${char}) ${char}${char}${char}-${char}${char}${char}${char}`;
        }
        return `${char}${char}${char}-${char}${char}${char}-${char}${char}${char}${char}`;
      case 'DATE_OF_BIRTH':
        if (entity.value.includes('/')) {
          return `${char}${char}/${char}${char}/${char}${char}${char}${char}`;
        }
        return `${char}${char}-${char}${char}-${char}${char}${char}${char}`;
      default:
        return char.repeat(entity.value.length);
    }
  }

  /**
   * Calculate confidence score for detected entity
   */
  private calculateConfidence(type: PhiType, value: string): number {
    // Simple confidence calculation based on pattern strength
    switch (type) {
      case 'SSN':
        return /^\d{3}-\d{2}-\d{4}$/.test(value) ? 0.95 : 0.7;
      case 'PHONE_NUMBER':
        return value.length >= 10 ? 0.9 : 0.6;
      case 'EMAIL':
        return value.includes('@') && value.includes('.') ? 0.95 : 0.5;
      case 'MEDICAL_RECORD_NUMBER':
        return value.toLowerCase().includes('mrn') ? 0.9 : 0.7;
      default:
        return 0.8;
    }
  }

  /**
   * Remove overlapping entities (keep highest confidence)
   */
  private removeOverlaps(entities: PhiEntity[]): PhiEntity[] {
    const result: PhiEntity[] = [];
    
    for (const entity of entities) {
      const hasOverlap = result.some(existing => 
        (entity.startIndex < existing.endIndex && entity.endIndex > existing.startIndex)
      );
      
      if (!hasOverlap) {
        result.push(entity);
      }
    }
    
    return result;
  }

  /**
   * Check if entity type should be redacted based on config
   */
  private shouldRedactType(type: PhiType): boolean {
    switch (type) {
      case 'PERSON_NAME':
        return this.config.redactNames;
      case 'DATE_OF_BIRTH':
        return this.config.redactDates;
      case 'ADDRESS':
        return this.config.redactAddresses;
      case 'PHONE_NUMBER':
        return this.config.redactPhones;
      case 'EMAIL':
        return this.config.redactEmails;
      case 'SSN':
      case 'MEDICAL_RECORD_NUMBER':
      case 'INSURANCE_ID':
      case 'ACCOUNT_NUMBER':
      case 'DEVICE_ID':
      case 'BIOMETRIC_ID':
        return this.config.redactIds;
      default:
        return true;
    }
  }
}
