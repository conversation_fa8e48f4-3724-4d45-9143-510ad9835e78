/**
 * Audit Logger Spy for HIPAA Compliance Testing
 * 
 * Provides in-memory audit logging for testing HIPAA compliance features.
 * Captures audit events without external dependencies.
 */

export type AuditEventType = 
  | 'PHI_ACCESS'
  | 'PHI_DETECTION'
  | 'PHI_REDACTION'
  | 'DOCUMENT_UPLOAD'
  | 'DOCUMENT_VIEW'
  | 'DOCUMENT_DOWNLOAD'
  | 'USER_LOGIN'
  | 'USER_LOGOUT'
  | 'PERMISSION_CHANGE'
  | 'DATA_EXPORT'
  | 'SYSTEM_ACCESS'
  | 'COMPLIANCE_VIOLATION';

export type AuditEvent = {
  id: string;
  timestamp: string;
  eventType: AuditEventType;
  userId?: string;
  tenantId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  resource?: string;
  action?: string;
  metadata: Record<string, any>;
  phiTypes?: string[];
  confidenceScore?: number;
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
};

export class AuditLoggerSpy {
  private events: AuditEvent[] = [];
  private eventCounter = 0;

  /**
   * Log an audit event
   */
  log(event: Omit<AuditEvent, 'id' | 'timestamp'>): string {
    const auditEvent: AuditEvent = {
      id: `audit-${++this.eventCounter}`,
      timestamp: new Date().toISOString(),
      ...event
    };

    this.events.push(auditEvent);
    return auditEvent.id;
  }

  /**
   * Log PHI access event
   */
  logPhiAccess(params: {
    userId: string;
    tenantId: string;
    matterId?: string;
    documentId?: string;
    accessType: 'view' | 'edit' | 'download' | 'print';
    phiTypes: string[];
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
  }): string {
    return this.log({
      eventType: 'PHI_ACCESS',
      userId: params.userId,
      tenantId: params.tenantId,
      sessionId: params.sessionId,
      ipAddress: params.ipAddress,
      userAgent: params.userAgent,
      action: params.accessType,
      phiTypes: params.phiTypes,
      metadata: {
        matterId: params.matterId,
        documentId: params.documentId,
        accessType: params.accessType,
        phiTypesCount: params.phiTypes.length
      }
    });
  }

  /**
   * Log PHI detection event
   */
  logPhiDetection(params: {
    userId: string;
    tenantId: string;
    documentId?: string;
    phiCount: number;
    phiTypes: string[];
    confidenceScore: number;
    sessionId?: string;
  }): string {
    return this.log({
      eventType: 'PHI_DETECTION',
      userId: params.userId,
      tenantId: params.tenantId,
      sessionId: params.sessionId,
      phiTypes: params.phiTypes,
      confidenceScore: params.confidenceScore,
      riskLevel: params.confidenceScore > 0.9 ? 'high' : params.confidenceScore > 0.7 ? 'medium' : 'low',
      metadata: {
        documentId: params.documentId,
        phiCount: params.phiCount,
        detectionAlgorithm: 'test-detector',
        processingTime: Math.random() * 1000 // Simulated processing time
      }
    });
  }

  /**
   * Log document upload event
   */
  logDocumentUpload(params: {
    userId: string;
    tenantId: string;
    documentId: string;
    fileName: string;
    fileSize?: number;
    sessionId?: string;
    ipAddress?: string;
  }): string {
    return this.log({
      eventType: 'DOCUMENT_UPLOAD',
      userId: params.userId,
      tenantId: params.tenantId,
      sessionId: params.sessionId,
      ipAddress: params.ipAddress,
      resource: params.documentId,
      action: 'upload',
      metadata: {
        fileName: params.fileName,
        fileSize: params.fileSize,
        uploadTimestamp: new Date().toISOString()
      }
    });
  }

  /**
   * Log compliance violation
   */
  logComplianceViolation(params: {
    userId?: string;
    tenantId?: string;
    violationType: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    sessionId?: string;
  }): string {
    return this.log({
      eventType: 'COMPLIANCE_VIOLATION',
      userId: params.userId,
      tenantId: params.tenantId,
      sessionId: params.sessionId,
      riskLevel: params.severity,
      metadata: {
        violationType: params.violationType,
        description: params.description,
        requiresInvestigation: params.severity === 'high' || params.severity === 'critical',
        autoReported: true
      }
    });
  }

  /**
   * Get all logged events
   */
  getEvents(): AuditEvent[] {
    return [...this.events];
  }

  /**
   * Get events by type
   */
  getEventsByType(eventType: AuditEventType): AuditEvent[] {
    return this.events.filter(event => event.eventType === eventType);
  }

  /**
   * Get events by user
   */
  getEventsByUser(userId: string): AuditEvent[] {
    return this.events.filter(event => event.userId === userId);
  }

  /**
   * Get events by tenant
   */
  getEventsByTenant(tenantId: string): AuditEvent[] {
    return this.events.filter(event => event.tenantId === tenantId);
  }

  /**
   * Get events by session
   */
  getEventsBySession(sessionId: string): AuditEvent[] {
    return this.events.filter(event => event.sessionId === sessionId);
  }

  /**
   * Get PHI-related events
   */
  getPhiEvents(): AuditEvent[] {
    return this.events.filter(event => 
      event.eventType === 'PHI_ACCESS' || 
      event.eventType === 'PHI_DETECTION' || 
      event.eventType === 'PHI_REDACTION'
    );
  }

  /**
   * Get high-risk events
   */
  getHighRiskEvents(): AuditEvent[] {
    return this.events.filter(event => 
      event.riskLevel === 'high' || event.riskLevel === 'critical'
    );
  }

  /**
   * Check if PHI was logged in metadata (compliance violation)
   */
  hasPhiInMetadata(): boolean {
    return this.events.some(event => {
      const metadata = JSON.stringify(event.metadata).toLowerCase();
      // Simple PHI patterns for testing
      return /\b\d{3}-\d{2}-\d{4}\b/.test(metadata) || // SSN
             /\b[a-z]+,\s[a-z]+\b/.test(metadata) || // Name pattern
             /\b\d{3}-\d{3}-\d{4}\b/.test(metadata); // Phone
    });
  }

  /**
   * Get event count by type
   */
  getEventCountByType(): Record<AuditEventType, number> {
    const counts = {} as Record<AuditEventType, number>;
    
    this.events.forEach(event => {
      counts[event.eventType] = (counts[event.eventType] || 0) + 1;
    });

    return counts;
  }

  /**
   * Clear all events
   */
  clear(): void {
    this.events = [];
    this.eventCounter = 0;
  }

  /**
   * Get events within time range
   */
  getEventsInTimeRange(startTime: Date, endTime: Date): AuditEvent[] {
    return this.events.filter(event => {
      const eventTime = new Date(event.timestamp);
      return eventTime >= startTime && eventTime <= endTime;
    });
  }

  /**
   * Export events as JSON for analysis
   */
  exportEvents(): string {
    return JSON.stringify(this.events, null, 2);
  }

  /**
   * Validate audit trail completeness for a session
   */
  validateSessionAuditTrail(sessionId: string): {
    isComplete: boolean;
    missingEvents: string[];
    eventCount: number;
  } {
    const sessionEvents = this.getEventsBySession(sessionId);
    const missingEvents: string[] = [];

    // Check for required events in a typical session
    const hasLogin = sessionEvents.some(e => e.eventType === 'USER_LOGIN');
    const hasLogout = sessionEvents.some(e => e.eventType === 'USER_LOGOUT');

    if (!hasLogin) missingEvents.push('USER_LOGIN');
    if (!hasLogout) missingEvents.push('USER_LOGOUT');

    return {
      isComplete: missingEvents.length === 0,
      missingEvents,
      eventCount: sessionEvents.length
    };
  }
}
