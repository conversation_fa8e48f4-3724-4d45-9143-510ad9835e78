/**
 * Deterministic LLM Stub for Testing
 * 
 * Provides predictable AI responses for testing medical copilot and other AI features.
 * Uses a mapping-based approach to ensure deterministic test results.
 */

export type LLMMessage = {
  role: 'system' | 'user' | 'assistant';
  content: string;
};

export interface LLMProvider {
  chat(messages: LLMMessage[]): Promise<LLMMessage>;
  streamChat?(messages: LLMMessage[]): AsyncGenerator<string, void, unknown>;
}

export class DeterministicLLM implements LLMProvider {
  private mapping: Record<string, string>;
  private defaultResponse: string;

  constructor(mapping: Record<string, string>, defaultResponse = '[[UNHANDLED_PROMPT]]') {
    this.mapping = mapping;
    this.defaultResponse = defaultResponse;
  }

  async chat(messages: LLMMessage[]): Promise<LLMMessage> {
    const lastUser = [...messages].reverse().find(m => m.role === 'user')?.content ?? '';
    const reply = this.findResponse(lastUser);
    
    return {
      role: 'assistant',
      content: reply
    };
  }

  async *streamChat(messages: LLMMessage[]): AsyncGenerator<string, void, unknown> {
    const lastUser = [...messages].reverse().find(m => m.role === 'user')?.content ?? '';
    const reply = this.findResponse(lastUser);
    
    // Simulate streaming by yielding chunks
    const chunks = reply.split(' ');
    for (const chunk of chunks) {
      yield chunk + ' ';
      // Small delay to simulate real streaming
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  private findResponse(userMessage: string): string {
    // Exact match first
    if (this.mapping[userMessage]) {
      return this.mapping[userMessage];
    }

    // Partial match for flexibility
    for (const [key, value] of Object.entries(this.mapping)) {
      if (userMessage.toLowerCase().includes(key.toLowerCase()) || 
          key.toLowerCase().includes(userMessage.toLowerCase())) {
        return value;
      }
    }

    return this.defaultResponse;
  }

  // Helper method to add new mappings during tests
  addMapping(prompt: string, response: string): void {
    this.mapping[prompt] = response;
  }

  // Helper method to clear mappings
  clearMappings(): void {
    this.mapping = {};
  }
}

/**
 * Medical-specific LLM stub with pre-configured medical responses
 */
export class MedicalLLMStub extends DeterministicLLM {
  constructor() {
    const medicalMappings = {
      'analyze medical record': 'Based on the medical record analysis, I found the following key findings: Patient presents with acute symptoms requiring immediate attention. Recommend further diagnostic testing.',
      'summarize patient history': 'Patient History Summary: 45-year-old patient with chronic conditions. Previous hospitalizations documented. Current medications reviewed.',
      'extract medical entities': 'Medical entities identified: Diagnosis - Hypertension, Medication - Lisinopril 10mg, Procedure - Blood pressure monitoring',
      'generate medical report': 'Medical Report: Patient evaluation completed. Clinical findings documented. Treatment plan established with follow-up recommendations.',
      'medical copilot help': 'I can assist with medical record analysis, patient history summarization, and clinical documentation. How can I help you today?'
    };

    super(medicalMappings, 'I am a medical AI assistant. Please provide more specific medical information for analysis.');
  }
}

/**
 * Legal-specific LLM stub for legal document analysis
 */
export class LegalLLMStub extends DeterministicLLM {
  constructor() {
    const legalMappings = {
      'analyze legal document': 'Legal document analysis complete. Key provisions identified. Potential issues flagged for review.',
      'extract legal entities': 'Legal entities found: Parties - John Doe vs. ABC Corp, Date - 2024-01-15, Jurisdiction - State Court',
      'summarize case': 'Case Summary: Personal injury matter involving motor vehicle accident. Liability disputed. Damages assessment pending.',
      'legal research': 'Legal research results: Relevant precedents identified. Applicable statutes reviewed. Case law analysis provided.'
    };

    super(legalMappings, 'I am a legal AI assistant. Please provide legal documents or questions for analysis.');
  }
}

/**
 * Factory for creating LLM stubs based on context
 */
export class LLMStubFactory {
  static createMedicalStub(): MedicalLLMStub {
    return new MedicalLLMStub();
  }

  static createLegalStub(): LegalLLMStub {
    return new LegalLLMStub();
  }

  static createCustomStub(mappings: Record<string, string>, defaultResponse?: string): DeterministicLLM {
    return new DeterministicLLM(mappings, defaultResponse);
  }

  static createEmptyStub(): DeterministicLLM {
    return new DeterministicLLM({}, 'No response configured for this prompt.');
  }
}

/**
 * LLM Provider Registry for test injection
 */
export class LLMProviderRegistry {
  private static instance: LLMProviderRegistry;
  private provider: LLMProvider | null = null;

  static getInstance(): LLMProviderRegistry {
    if (!LLMProviderRegistry.instance) {
      LLMProviderRegistry.instance = new LLMProviderRegistry();
    }
    return LLMProviderRegistry.instance;
  }

  setProvider(provider: LLMProvider): void {
    this.provider = provider;
  }

  getProvider(): LLMProvider | null {
    return this.provider;
  }

  clearProvider(): void {
    this.provider = null;
  }

  // Helper for tests to inject deterministic responses
  static injectTestProvider(mappings: Record<string, string>): void {
    const registry = LLMProviderRegistry.getInstance();
    const stub = new DeterministicLLM(mappings);
    registry.setProvider(stub);
  }

  static clearTestProvider(): void {
    const registry = LLMProviderRegistry.getInstance();
    registry.clearProvider();
  }
}

/**
 * Test utilities for LLM testing
 */
export const LLMTestUtils = {
  /**
   * Create a simple stub that always returns the same response
   */
  createSimpleStub: (response: string): DeterministicLLM => {
    return new DeterministicLLM({}, response);
  },

  /**
   * Create a stub that responds to specific keywords
   */
  createKeywordStub: (keywords: Record<string, string>): DeterministicLLM => {
    return new DeterministicLLM(keywords);
  },

  /**
   * Verify that an LLM response contains expected content
   */
  verifyResponse: (response: string, expectedKeywords: string[]): boolean => {
    return expectedKeywords.every(keyword => 
      response.toLowerCase().includes(keyword.toLowerCase())
    );
  },

  /**
   * Extract structured data from LLM response (for testing)
   */
  extractStructuredData: (response: string): Record<string, any> => {
    try {
      // Try to parse JSON if response contains structured data
      const jsonMatch = response.match(/\{.*\}/s);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      // Fallback to simple key-value extraction
    }
    
    return { rawResponse: response };
  }
};
