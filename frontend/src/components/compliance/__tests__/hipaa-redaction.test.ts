/**
 * HIPAA Redaction Unit Tests
 * 
 * Tests PHI detection, redaction, and audit logging functionality
 * for HIPAA compliance requirements.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PhiRedactor, PhiType, DEFAULT_REDACTION_CONFIG } from '@/test/infra/compliance/redaction';
import { AuditLoggerSpy } from '@/test/infra/compliance/audit-logger.spy';
import phiTestCases from '@/test/infra/compliance/fixtures/phi-test-cases.json';
import sampleMedicalNotes from '@/test/infra/compliance/fixtures/sample-medical-notes.txt?raw';

describe('HIPAA PHI Redaction', () => {
  let redactor: PhiRedactor;
  let auditLogger: AuditLoggerSpy;

  beforeEach(() => {
    redactor = new PhiRedactor(DEFAULT_REDACTION_CONFIG);
    auditLogger = new AuditLoggerSpy();
  });

  afterEach(() => {
    auditLogger.clear();
  });

  describe('PHI Detection', () => {
    it('should detect high-risk PHI elements', () => {
      const testCase = phiTestCases.testCases.highRiskPhi;
      const result = redactor.detectPhi(testCase.text);

      expect(result).toHaveLength(testCase.expectedEntities.length);
      
      // Verify each expected entity is detected
      testCase.expectedEntities.forEach(expectedEntity => {
        const found = result.find(entity => 
          entity.type === expectedEntity.type && 
          entity.value === expectedEntity.value
        );
        expect(found).toBeDefined();
        expect(found?.confidence).toBeGreaterThanOrEqual(expectedEntity.confidence);
      });
    });

    it('should detect SSN patterns correctly', () => {
      const testTexts = [
        'SSN: ***********',
        'Social Security Number: ***********',
        'Patient SSN ***********'
      ];

      testTexts.forEach(text => {
        const entities = redactor.detectPhi(text);
        const ssnEntity = entities.find(e => e.type === 'SSN');
        
        expect(ssnEntity).toBeDefined();
        expect(ssnEntity?.confidence).toBeGreaterThan(0.8);
      });
    });

    it('should detect medical record numbers', () => {
      const testTexts = [
        'MRN: MRN123456',
        'Medical Record Number: ABC789012',
        'Patient ID: PAT-2024-001'
      ];

      testTexts.forEach(text => {
        const entities = redactor.detectPhi(text);
        const mrnEntity = entities.find(e => e.type === 'MEDICAL_RECORD_NUMBER');
        
        expect(mrnEntity).toBeDefined();
        expect(mrnEntity?.confidence).toBeGreaterThan(0.7);
      });
    });

    it('should detect phone numbers in various formats', () => {
      const testTexts = [
        '(*************',
        '************',
        '+1 ************',
        '************'
      ];

      testTexts.forEach(text => {
        const entities = redactor.detectPhi(text);
        const phoneEntity = entities.find(e => e.type === 'PHONE_NUMBER');
        
        expect(phoneEntity).toBeDefined();
        expect(phoneEntity?.confidence).toBeGreaterThan(0.6);
      });
    });

    it('should handle edge cases without false positives', () => {
      const testCase = phiTestCases.testCases.edgeCases;
      const entities = redactor.detectPhi(testCase.text);

      // Should not detect conference room numbers as PHI
      expect(entities).toHaveLength(0);
    });
  });

  describe('PHI Redaction', () => {
    it('should redact PHI with standard placeholders', () => {
      const testCase = phiTestCases.redactionTests.standardRedaction;
      const result = redactor.redactPhi(testCase.input);

      expect(result.redactedText).toContain('[REDACTED_NAME]');
      expect(result.redactedText).toContain('[REDACTED_DATE]');
      expect(result.redactedText).toContain('[REDACTED_SSN]');
      expect(result.redactionCount).toBeGreaterThan(0);
    });

    it('should preserve format when configured', () => {
      const preserveFormatRedactor = new PhiRedactor({
        ...DEFAULT_REDACTION_CONFIG,
        preserveFormat: true,
        redactionChar: '*'
      });

      const testCase = phiTestCases.redactionTests.preserveFormat;
      const result = preserveFormatRedactor.redactPhi(testCase.input);

      expect(result.redactedText).toMatch(/\*\*\*-\*\*-\*\*\*\*/); // SSN format
      expect(result.redactedText).toMatch(/\(\*\*\*\) \*\*\*-\*\*\*\*/); // Phone format
    });

    it('should support selective redaction', () => {
      const selectiveRedactor = new PhiRedactor({
        enabled: true,
        redactNames: false,
        redactDates: false,
        redactAddresses: true,
        redactPhones: true,
        redactEmails: true,
        redactIds: true,
        preserveFormat: false,
        redactionChar: '*'
      });

      const testCase = phiTestCases.redactionTests.selectiveRedaction;
      const result = selectiveRedactor.redactPhi(testCase.input);

      // Names and dates should remain
      expect(result.redactedText).toContain('Dr. Johnson');
      expect(result.redactedText).toContain('03/15/2024');
      
      // Addresses should be redacted
      expect(result.redactedText).toContain('[REDACTED_ADDRESS]');
    });

    it('should handle real medical notes', () => {
      const result = redactor.redactPhi(sampleMedicalNotes);

      expect(result.redactionCount).toBeGreaterThan(10);
      expect(result.redactedText).not.toContain('***********'); // SSN should be redacted
      expect(result.redactedText).not.toContain('<EMAIL>'); // Email should be redacted
      expect(result.redactedText).not.toContain('(*************'); // Phone should be redacted
    });
  });

  describe('PHI Validation', () => {
    it('should validate clean text has no PHI', () => {
      const cleanText = 'Patient reports feeling better after treatment. Continue current medication.';
      const validation = redactor.validateNoPhi(cleanText);

      expect(validation.isClean).toBe(true);
      expect(validation.foundPhi).toHaveLength(0);
      expect(validation.riskLevel).toBe('low');
    });

    it('should identify high-risk PHI in text', () => {
      const riskyText = 'Patient John Doe, SSN: ***********, phone: (*************';
      const validation = redactor.validateNoPhi(riskyText);

      expect(validation.isClean).toBe(false);
      expect(validation.foundPhi.length).toBeGreaterThan(0);
      expect(validation.riskLevel).toBe('high');
    });

    it('should categorize medium-risk PHI correctly', () => {
      const mediumRiskText = 'Patient visited on 03/15/2024 at 123 Main Street.';
      const validation = redactor.validateNoPhi(mediumRiskText);

      expect(validation.isClean).toBe(false);
      expect(validation.riskLevel).toBe('medium');
    });
  });

  describe('Audit Logging Integration', () => {
    it('should log PHI detection events', () => {
      const testText = 'Patient John Smith, SSN: ***********';
      const entities = redactor.detectPhi(testText);

      // Simulate audit logging
      auditLogger.logPhiDetection({
        userId: 'test-user-1',
        tenantId: 'test-tenant-1',
        documentId: 'test-doc-1',
        phiCount: entities.length,
        phiTypes: entities.map(e => e.type),
        confidenceScore: Math.max(...entities.map(e => e.confidence)),
        sessionId: 'test-session-1'
      });

      const phiEvents = auditLogger.getPhiEvents();
      expect(phiEvents).toHaveLength(1);
      expect(phiEvents[0].eventType).toBe('PHI_DETECTION');
      expect(phiEvents[0].phiTypes).toContain('PERSON_NAME');
      expect(phiEvents[0].phiTypes).toContain('SSN');
    });

    it('should log PHI access events', () => {
      auditLogger.logPhiAccess({
        userId: 'test-user-1',
        tenantId: 'test-tenant-1',
        matterId: 'matter-1',
        documentId: 'doc-1',
        accessType: 'view',
        phiTypes: ['PERSON_NAME', 'SSN', 'PHONE_NUMBER'],
        sessionId: 'test-session-1',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 Test Browser'
      });

      const accessEvents = auditLogger.getEventsByType('PHI_ACCESS');
      expect(accessEvents).toHaveLength(1);
      expect(accessEvents[0].action).toBe('view');
      expect(accessEvents[0].phiTypes).toHaveLength(3);
      expect(accessEvents[0].metadata.matterId).toBe('matter-1');
    });

    it('should detect compliance violations', () => {
      // Simulate PHI in metadata (compliance violation)
      auditLogger.log({
        eventType: 'DOCUMENT_VIEW',
        userId: 'test-user-1',
        tenantId: 'test-tenant-1',
        metadata: {
          patientName: 'John Smith', // This is a violation - PHI in metadata
          ssn: '***********' // This is a violation - PHI in metadata
        }
      });

      const hasPhiInMetadata = auditLogger.hasPhiInMetadata();
      expect(hasPhiInMetadata).toBe(true);

      // Log the violation
      auditLogger.logComplianceViolation({
        userId: 'test-user-1',
        tenantId: 'test-tenant-1',
        violationType: 'PHI_IN_METADATA',
        description: 'PHI detected in audit log metadata',
        severity: 'high',
        sessionId: 'test-session-1'
      });

      const violations = auditLogger.getEventsByType('COMPLIANCE_VIOLATION');
      expect(violations).toHaveLength(1);
      expect(violations[0].riskLevel).toBe('high');
    });

    it('should validate session audit trail completeness', () => {
      const sessionId = 'test-session-complete';

      // Log session events
      auditLogger.log({
        eventType: 'USER_LOGIN',
        userId: 'test-user-1',
        sessionId,
        metadata: { loginMethod: 'password' }
      });

      auditLogger.logPhiAccess({
        userId: 'test-user-1',
        tenantId: 'test-tenant-1',
        accessType: 'view',
        phiTypes: ['PERSON_NAME'],
        sessionId
      });

      auditLogger.log({
        eventType: 'USER_LOGOUT',
        userId: 'test-user-1',
        sessionId,
        metadata: { logoutReason: 'manual' }
      });

      const validation = auditLogger.validateSessionAuditTrail(sessionId);
      expect(validation.isComplete).toBe(true);
      expect(validation.missingEvents).toHaveLength(0);
      expect(validation.eventCount).toBe(3);
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle large documents efficiently', () => {
      const largeText = sampleMedicalNotes.repeat(100); // ~100x larger
      const startTime = performance.now();
      
      const result = redactor.redactPhi(largeText);
      
      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(processingTime).toBeLessThan(5000); // Should complete within 5 seconds
      expect(result.redactionCount).toBeGreaterThan(0);
    });

    it('should handle empty and null inputs gracefully', () => {
      expect(() => redactor.detectPhi('')).not.toThrow();
      expect(() => redactor.redactPhi('')).not.toThrow();
      expect(() => redactor.validateNoPhi('')).not.toThrow();

      const emptyResult = redactor.detectPhi('');
      expect(emptyResult).toHaveLength(0);
    });

    it('should handle overlapping PHI entities correctly', () => {
      const overlappingText = 'Dr. John <NAME_EMAIL>';
      const entities = redactor.detectPhi(overlappingText);

      // Should detect both name and email without overlap issues
      const nameEntity = entities.find(e => e.type === 'PERSON_NAME');
      const emailEntity = entities.find(e => e.type === 'EMAIL');

      expect(nameEntity).toBeDefined();
      expect(emailEntity).toBeDefined();
      
      // Entities should not overlap
      if (nameEntity && emailEntity) {
        const hasOverlap = (nameEntity.startIndex < emailEntity.endIndex && 
                           nameEntity.endIndex > emailEntity.startIndex);
        expect(hasOverlap).toBe(false);
      }
    });
  });
});
