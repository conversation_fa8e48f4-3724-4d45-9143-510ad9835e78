/**
 * Medical Copilot Integration Tests
 * 
 * Tests medical AI copilot functionality with deterministic LLM responses
 * and proper PHI handling in an integration environment.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { 
  LLMStubFactory, 
  LLMProviderRegistry, 
  MedicalLLMStub 
} from '@/test/infra/ai/llm-stub';
import { 
  AuditLoggerSpy 
} from '@/test/infra/compliance/audit-logger.spy';
import { 
  PhiRedactor, 
  DEFAULT_REDACTION_CONFIG 
} from '@/test/infra/compliance/redaction';
import { 
  MSWTestUtils, 
  ServerPresets 
} from '@/test/infra/http/msw-server';
import { 
  IntegrationTestUtils 
} from '@/test/integration.setup';
import medicalPrompts from '@/test/infra/ai/fixtures/medical-prompts.json';

// Mock the medical copilot component (we'll create a simple version for testing)
const MockMedicalCopilot = ({ onAnalysis }: { onAnalysis?: (result: any) => void }) => {
  const [input, setInput] = React.useState('');
  const [response, setResponse] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);

  const handleSubmit = async () => {
    setIsLoading(true);
    
    try {
      // Simulate API call to medical copilot
      const response = await fetch('/api/medical/copilot/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: input })
      });
      
      const data = await response.json();
      setResponse(data.response);
      onAnalysis?.(data);
    } catch (error) {
      setResponse('Error occurred during analysis');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div data-testid="medical-copilot">
      <textarea
        data-testid="medical-input"
        value={input}
        onChange={(e) => setInput(e.target.value)}
        placeholder="Enter medical query..."
      />
      <button
        data-testid="analyze-button"
        onClick={handleSubmit}
        disabled={isLoading}
      >
        {isLoading ? 'Analyzing...' : 'Analyze'}
      </button>
      <div data-testid="medical-response">
        {response}
      </div>
    </div>
  );
};

// Mock React import
const React = {
  useState: vi.fn().mockImplementation((initial) => {
    let state = initial;
    const setState = (newState: any) => {
      state = typeof newState === 'function' ? newState(state) : newState;
    };
    return [state, setState];
  })
};

describe('Medical Copilot Integration', () => {
  let medicalLLM: MedicalLLMStub;
  let auditLogger: AuditLoggerSpy;
  let phiRedactor: PhiRedactor;

  beforeEach(() => {
    // Setup medical LLM stub
    medicalLLM = LLMStubFactory.createMedicalStub();
    LLMProviderRegistry.getInstance().setProvider(medicalLLM);

    // Setup audit logger
    auditLogger = IntegrationTestUtils.getAuditLogger();

    // Setup PHI redactor
    phiRedactor = new PhiRedactor(DEFAULT_REDACTION_CONFIG);

    // Setup MSW with medical handlers
    ServerPresets.medicalCopilot();

    // Setup test session
    IntegrationTestUtils.setupTestSession('medical-user-1', 'medical-tenant-1');
  });

  afterEach(() => {
    // Cleanup
    LLMProviderRegistry.clearTestProvider();
    IntegrationTestUtils.clearTestSession();
    IntegrationTestUtils.resetMocks();
  });

  describe('Basic Medical Analysis', () => {
    it('should analyze medical records with deterministic responses', async () => {
      const analysisResults: any[] = [];
      
      render(
        <MockMedicalCopilot 
          onAnalysis={(result) => analysisResults.push(result)} 
        />
      );

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      // Test basic medical analysis
      fireEvent.change(input, { 
        target: { value: medicalPrompts.medicalAnalysisPrompts.basicAnalysis } 
      });
      fireEvent.click(analyzeButton);

      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        expect(response.textContent).toContain('medical record analysis');
        expect(response.textContent).toContain('clinical evaluation');
      });

      // Verify analysis result
      expect(analysisResults).toHaveLength(1);
      expect(analysisResults[0].confidence).toBeGreaterThan(0.8);
    });

    it('should handle symptom analysis requests', async () => {
      render(<MockMedicalCopilot />);

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      fireEvent.change(input, { 
        target: { value: medicalPrompts.medicalAnalysisPrompts.symptomAnalysis } 
      });
      fireEvent.click(analyzeButton);

      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        expect(response.textContent).toContain('vestibular dysfunction');
        expect(response.textContent).toContain('neurological evaluation');
      });
    });

    it('should provide medication review analysis', async () => {
      render(<MockMedicalCopilot />);

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      fireEvent.change(input, { 
        target: { value: medicalPrompts.medicalAnalysisPrompts.medicationReview } 
      });
      fireEvent.click(analyzeButton);

      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        expect(response.textContent).toContain('Lisinopril');
        expect(response.textContent).toContain('Metformin');
        expect(response.textContent).toContain('drug interactions');
      });
    });
  });

  describe('PHI Handling in Medical Copilot', () => {
    it('should detect and redact PHI in medical queries', async () => {
      const phiQuery = 'Analyze record for patient John Smith, SSN: ***********, DOB: 01/15/1980';
      
      // Detect PHI in the query
      const phiEntities = phiRedactor.detectPhi(phiQuery);
      expect(phiEntities.length).toBeGreaterThan(0);

      // Redact PHI before processing
      const redactionResult = phiRedactor.redactPhi(phiQuery);
      expect(redactionResult.redactedText).not.toContain('John Smith');
      expect(redactionResult.redactedText).not.toContain('***********');

      // Log PHI detection
      auditLogger.logPhiDetection({
        userId: 'medical-user-1',
        tenantId: 'medical-tenant-1',
        documentId: 'medical-query-1',
        phiCount: phiEntities.length,
        phiTypes: phiEntities.map(e => e.type),
        confidenceScore: Math.max(...phiEntities.map(e => e.confidence)),
        sessionId: 'medical-session-1'
      });

      const phiEvents = auditLogger.getPhiEvents();
      expect(phiEvents).toHaveLength(1);
      expect(phiEvents[0].eventType).toBe('PHI_DETECTION');
    });

    it('should audit medical copilot interactions', async () => {
      render(<MockMedicalCopilot />);

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      // Simulate medical copilot interaction
      fireEvent.change(input, { 
        target: { value: 'Analyze patient symptoms for diagnosis' } 
      });
      fireEvent.click(analyzeButton);

      // Log the interaction
      auditLogger.log({
        eventType: 'SYSTEM_ACCESS',
        userId: 'medical-user-1',
        tenantId: 'medical-tenant-1',
        resource: 'medical-copilot',
        action: 'analyze',
        metadata: {
          queryType: 'symptom-analysis',
          hasPhiDetection: false,
          processingTime: 1250
        }
      });

      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        expect(response.textContent).toBeTruthy();
      });

      const systemEvents = auditLogger.getEventsByType('SYSTEM_ACCESS');
      expect(systemEvents).toHaveLength(1);
      expect(systemEvents[0].resource).toBe('medical-copilot');
    });

    it('should handle PHI in medical copilot responses', async () => {
      // Configure LLM to return response with potential PHI
      medicalLLM.addMapping(
        'patient analysis',
        'Patient John Doe shows signs of hypertension. Contact at (555) 123-4567 for follow-up.'
      );

      render(<MockMedicalCopilot />);

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      fireEvent.change(input, { target: { value: 'patient analysis' } });
      fireEvent.click(analyzeButton);

      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        const responseText = response.textContent || '';
        
        // Check if response contains PHI
        const validation = phiRedactor.validateNoPhi(responseText);
        
        if (!validation.isClean) {
          // Log compliance violation
          auditLogger.logComplianceViolation({
            userId: 'medical-user-1',
            tenantId: 'medical-tenant-1',
            violationType: 'PHI_IN_RESPONSE',
            description: 'Medical copilot response contains PHI',
            severity: 'high',
            sessionId: 'medical-session-1'
          });
        }

        expect(validation.foundPhi.length).toBeGreaterThan(0);
      });

      const violations = auditLogger.getEventsByType('COMPLIANCE_VIOLATION');
      expect(violations).toHaveLength(1);
    });
  });

  describe('Medical Copilot Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      // Mock API error
      MSWTestUtils.mockError('/api/medical/copilot/chat', 500, {
        error: 'Medical analysis service unavailable'
      });

      render(<MockMedicalCopilot />);

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      fireEvent.change(input, { target: { value: 'analyze symptoms' } });
      fireEvent.click(analyzeButton);

      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        expect(response.textContent).toContain('Error occurred');
      });
    });

    it('should handle timeout scenarios', async () => {
      // Mock slow response
      MSWTestUtils.mockTimeout('/api/medical/copilot/chat', 10000);

      render(<MockMedicalCopilot />);

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      fireEvent.change(input, { target: { value: 'analyze complex case' } });
      fireEvent.click(analyzeButton);

      // Should show loading state
      expect(screen.getByText('Analyzing...')).toBeInTheDocument();

      // Wait for timeout
      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        expect(response.textContent).toContain('Error occurred');
      }, { timeout: 15000 });
    });

    it('should validate medical copilot input', async () => {
      render(<MockMedicalCopilot />);

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      // Test empty input
      fireEvent.change(input, { target: { value: '' } });
      fireEvent.click(analyzeButton);

      // Should handle empty input gracefully
      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        expect(response.textContent).toBeTruthy();
      });
    });
  });

  describe('Medical Copilot Performance', () => {
    it('should complete analysis within acceptable time limits', async () => {
      const startTime = performance.now();
      
      render(<MockMedicalCopilot />);

      const input = screen.getByTestId('medical-input');
      const analyzeButton = screen.getByTestId('analyze-button');

      fireEvent.change(input, { 
        target: { value: medicalPrompts.medicalAnalysisPrompts.basicAnalysis } 
      });
      fireEvent.click(analyzeButton);

      await waitFor(() => {
        const response = screen.getByTestId('medical-response');
        expect(response.textContent).toBeTruthy();
      });

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      // Should complete within 5 seconds
      expect(processingTime).toBeLessThan(5000);
    });

    it('should handle concurrent medical analyses', async () => {
      const analysisPromises: Promise<any>[] = [];

      // Simulate multiple concurrent analyses
      for (let i = 0; i < 3; i++) {
        const promise = fetch('/api/medical/copilot/chat', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            message: `Medical analysis request ${i + 1}` 
          })
        }).then(res => res.json());
        
        analysisPromises.push(promise);
      }

      const results = await Promise.all(analysisPromises);
      
      // All requests should complete successfully
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.response).toBeTruthy();
        expect(result.confidence).toBeGreaterThan(0);
      });
    });
  });

  describe('Medical Copilot Integration with External Services', () => {
    it('should integrate with PHI detection service', async () => {
      // Mock PHI detection API
      MSWTestUtils.mockSuccess('/api/medical/phi-detection', {
        phiDetected: true,
        entities: [
          { type: 'PERSON_NAME', confidence: 0.95 },
          { type: 'SSN', confidence: 0.98 }
        ],
        confidenceScore: 0.96
      });

      const phiText = 'Patient John Smith, SSN: ***********';
      
      const response = await fetch('/api/medical/phi-detection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: phiText })
      });

      const result = await response.json();
      
      expect(result.phiDetected).toBe(true);
      expect(result.entities).toHaveLength(2);
      expect(result.confidenceScore).toBeGreaterThan(0.9);
    });

    it('should maintain audit trail for medical workflows', async () => {
      const sessionId = 'medical-workflow-session';

      // Simulate complete medical workflow
      auditLogger.log({
        eventType: 'USER_LOGIN',
        userId: 'medical-user-1',
        sessionId,
        metadata: { userRole: 'medical-professional' }
      });

      auditLogger.log({
        eventType: 'DOCUMENT_UPLOAD',
        userId: 'medical-user-1',
        sessionId,
        resource: 'medical-record-1',
        metadata: { documentType: 'medical-record' }
      });

      auditLogger.logPhiDetection({
        userId: 'medical-user-1',
        tenantId: 'medical-tenant-1',
        documentId: 'medical-record-1',
        phiCount: 5,
        phiTypes: ['PERSON_NAME', 'SSN', 'PHONE_NUMBER'],
        confidenceScore: 0.92,
        sessionId
      });

      auditLogger.log({
        eventType: 'SYSTEM_ACCESS',
        userId: 'medical-user-1',
        sessionId,
        resource: 'medical-copilot',
        action: 'analyze',
        metadata: { analysisType: 'comprehensive' }
      });

      auditLogger.log({
        eventType: 'USER_LOGOUT',
        userId: 'medical-user-1',
        sessionId,
        metadata: { sessionDuration: 1800000 } // 30 minutes
      });

      const sessionEvents = auditLogger.getEventsBySession(sessionId);
      expect(sessionEvents).toHaveLength(5);

      const validation = auditLogger.validateSessionAuditTrail(sessionId);
      expect(validation.isComplete).toBe(true);
      expect(validation.eventCount).toBe(5);
    });
  });
});
