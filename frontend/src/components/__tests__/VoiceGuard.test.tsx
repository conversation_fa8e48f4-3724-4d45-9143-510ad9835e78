import React from "react";
import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { useAuth } from "@/lib/auth/useAuth";
import { useFeatures } from "@/hooks/useFeatures";
import VoiceOverviewPage from "@/app/(dashboard)/admin/voice/page";

// Mock the hooks
vi.mock("next/navigation", () => ({
  redirect: vi.fn(),
  useRouter: vi.fn(),
}));

vi.mock("@/lib/auth/useAuth");
vi.mock("@/hooks/useFeatures");

const mockUseAuth = vi.mocked(useAuth);
const mockUseFeatures = vi.mocked(useFeatures);

describe("Voice Guard", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should redirect when user is not a partner", () => {
    const mockRedirect = vi.fn();
    vi.doMock("next/navigation", () => ({
      redirect: mockRedirect,
    }));

    mockUseAuth.mockReturnValue({
      profile: { role: "attorney", tenant_id: "test-tenant" },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: vi.fn().mockReturnValue(true),
      isLoading: false,
      features: ["voice_intake"],
      error: null,
      hasAllFeatures: vi.fn(),
      hasAnyFeature: vi.fn(),
    });

    render(<VoiceOverviewPage />);

    expect(mockRedirect).toHaveBeenCalledWith("/");
  });

  it("should redirect when user does not have voice_intake feature", () => {
    const mockRedirect = vi.fn();
    vi.doMock("next/navigation", () => ({
      redirect: mockRedirect,
    }));

    mockUseAuth.mockReturnValue({
      profile: { role: "partner", tenant_id: "test-tenant" },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: vi.fn().mockReturnValue(false),
      isLoading: false,
      features: [],
      error: null,
      hasAllFeatures: vi.fn(),
      hasAnyFeature: vi.fn(),
    });

    render(<VoiceOverviewPage />);

    expect(mockRedirect).toHaveBeenCalledWith("/");
  });

  it("should render voice overview when user is partner with voice_intake feature", () => {
    mockUseAuth.mockReturnValue({
      profile: { role: "partner", tenant_id: "test-tenant" },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: vi.fn().mockReturnValue(true),
      isLoading: false,
      features: ["voice_intake"],
      error: null,
      hasAllFeatures: vi.fn(),
      hasAnyFeature: vi.fn(),
    });

    render(<VoiceOverviewPage />);

    // Should not redirect and should render the page
    expect(screen.getByText(/voice/i)).toBeInTheDocument();
  });

  it("should handle loading state", () => {
    mockUseAuth.mockReturnValue({
      profile: null,
      isAuthenticated: false,
      isLoading: true,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: vi.fn(),
      isLoading: true,
      features: [],
      error: null,
      hasAllFeatures: vi.fn(),
      hasAnyFeature: vi.fn(),
    });

    render(<VoiceOverviewPage />);

    // Should show loading state or not redirect while loading
    expect(screen.queryByText(/loading/i)).toBeInTheDocument();
  });

  it("should handle unauthenticated user", () => {
    const mockRedirect = vi.fn();
    vi.doMock("next/navigation", () => ({
      redirect: mockRedirect,
    }));

    mockUseAuth.mockReturnValue({
      profile: null,
      isAuthenticated: false,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: vi.fn().mockReturnValue(false),
      isLoading: false,
      features: [],
      error: null,
      hasAllFeatures: vi.fn(),
      hasAnyFeature: vi.fn(),
    });

    render(<VoiceOverviewPage />);

    expect(mockRedirect).toHaveBeenCalledWith("/");
  });

  it("should handle feature loading error", () => {
    const mockRedirect = vi.fn();
    vi.doMock("next/navigation", () => ({
      redirect: mockRedirect,
    }));

    mockUseAuth.mockReturnValue({
      profile: { role: "partner", tenant_id: "test-tenant" },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: vi.fn().mockReturnValue(false),
      isLoading: false,
      features: [],
      error: new Error("Failed to load features"),
      hasAllFeatures: vi.fn(),
      hasAnyFeature: vi.fn(),
    });

    render(<VoiceOverviewPage />);

    // Should redirect when there's an error loading features
    expect(mockRedirect).toHaveBeenCalledWith("/");
  });
});
