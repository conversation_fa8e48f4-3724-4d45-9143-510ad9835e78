import React from "react";
import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { SubscriptionBadge } from "@/components/subscription-badge";
import { getPlanBadgeColor, getPlanDisplayName } from "@/lib/subscription";

// Mock the dependencies
vi.mock("@/lib/supabase/provider", () => ({
  useSupabase: () => ({
    _supabase: {},
  }),
}));

vi.mock("@/lib/auth/useAuth", () => ({
  useAuth: () => ({
    profile: { tenant_id: "test-tenant" },
  }),
}));

vi.mock("@/lib/subscription", () => ({
  getCurrentPlan: vi.fn(),
  getPlanBadgeColor: vi.fn(),
  getPlanDisplayName: vi.fn(),
}));

const mockGetCurrentPlan = vi.fn();
const mockGetPlanBadgeColor = vi.mocked(getPlanBadgeColor);
const mockGetPlanDisplayName = vi.mocked(getPlanDisplayName);

describe("SubscriptionBadge", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockGetPlanBadgeColor.mockReturnValue(
      "bg-slate-100 text-slate-700 border-slate-200",
    );
    mockGetPlanDisplayName.mockReturnValue("Core");
  });

  it("renders with plan code prop", () => {
    render(<SubscriptionBadge planCode="core" />);

    expect(screen.getByText("Core")).toBeInTheDocument();
  });

  it("applies correct colors for different plan codes", () => {
    mockGetPlanBadgeColor.mockReturnValue(
      "bg-orange-100 text-orange-700 border-orange-200",
    );
    mockGetPlanDisplayName.mockReturnValue("Professional");

    const { container } = render(<SubscriptionBadge planCode="professional" />);

    expect(screen.getByText("Professional")).toBeInTheDocument();
    expect(mockGetPlanBadgeColor).toHaveBeenCalledWith("professional");
    
    const badge = container.querySelector(".bg-orange-100");
    expect(badge).toBeInTheDocument();
  });

  it("renders with enterprise plan styling", () => {
    mockGetPlanBadgeColor.mockReturnValue(
      "bg-purple-100 text-purple-700 border-purple-200",
    );
    mockGetPlanDisplayName.mockReturnValue("Enterprise");

    const { container } = render(<SubscriptionBadge planCode="enterprise" />);

    expect(screen.getByText("Enterprise")).toBeInTheDocument();
    expect(mockGetPlanBadgeColor).toHaveBeenCalledWith("enterprise");
    
    const badge = container.querySelector(".bg-purple-100");
    expect(badge).toBeInTheDocument();
  });

  it("handles unknown plan codes gracefully", () => {
    mockGetPlanBadgeColor.mockReturnValue(
      "bg-gray-100 text-gray-700 border-gray-200",
    );
    mockGetPlanDisplayName.mockReturnValue("Unknown");

    render(<SubscriptionBadge planCode="unknown-plan" />);

    expect(screen.getByText("Unknown")).toBeInTheDocument();
    expect(mockGetPlanBadgeColor).toHaveBeenCalledWith("unknown-plan");
  });

  it("renders with custom className", () => {
    const { container } = render(
      <SubscriptionBadge planCode="core" className="custom-class" />,
    );

    const badge = container.querySelector(".custom-class");
    expect(badge).toBeInTheDocument();
  });

  it("renders with small size variant", () => {
    const { container } = render(
      <SubscriptionBadge planCode="core" size="sm" />,
    );

    // Check for small size styling
    const badge = container.querySelector(".text-xs");
    expect(badge).toBeInTheDocument();
  });

  it("renders with large size variant", () => {
    const { container } = render(
      <SubscriptionBadge planCode="core" size="lg" />,
    );

    // Check for large size styling
    const badge = container.querySelector(".text-base");
    expect(badge).toBeInTheDocument();
  });

  it("handles click events when clickable", () => {
    const handleClick = vi.fn();

    render(
      <SubscriptionBadge 
        planCode="core" 
        onClick={handleClick}
        clickable={true}
      />,
    );

    const badge = screen.getByText("Core");
    fireEvent.click(badge);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("does not handle click events when not clickable", () => {
    const handleClick = vi.fn();

    render(
      <SubscriptionBadge 
        planCode="core" 
        onClick={handleClick}
        clickable={false}
      />,
    );

    const badge = screen.getByText("Core");
    fireEvent.click(badge);

    expect(handleClick).not.toHaveBeenCalled();
  });

  it("shows upgrade indicator for lower tier plans", () => {
    render(
      <SubscriptionBadge 
        planCode="core" 
        showUpgradeIndicator={true}
      />,
    );

    expect(screen.getByText("Core")).toBeInTheDocument();
    // Check for upgrade indicator (could be an icon or text)
    const upgradeIndicator = screen.queryByText("↑") || screen.queryByRole("button");
    expect(upgradeIndicator).toBeInTheDocument();
  });

  it("does not show upgrade indicator for enterprise plans", () => {
    mockGetPlanDisplayName.mockReturnValue("Enterprise");

    render(
      <SubscriptionBadge 
        planCode="enterprise" 
        showUpgradeIndicator={true}
      />,
    );

    expect(screen.getByText("Enterprise")).toBeInTheDocument();
    // Enterprise plans should not show upgrade indicator
    const upgradeIndicator = screen.queryByText("↑");
    expect(upgradeIndicator).not.toBeInTheDocument();
  });
});
