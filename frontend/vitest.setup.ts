import { vi } from "vitest";
import { webcrypto } from "node:crypto";

// 1) WebCrypto for jose (JWKS tests)
if (!globalThis.crypto?.subtle) {
  // @ts-expect-error test shim
  globalThis.crypto = webcrypto as any;
}

// 2) React global for components that don't import React explicitly
import React from "react";
// @ts-expect-error test shim
globalThis.React = React;

// 3) Next "virtual" modules used by server code (don't exist in Vitest)
vi.mock("server-only", () => ({}));
vi.mock("next/headers", () => ({
  headers: () => new Headers(),
  cookies: () => ({ get: vi.fn(), set: vi.fn(), delete: vi.fn() }),
}));
vi.mock("next/navigation", () => ({
  redirect: vi.fn(),
  useRouter: () => ({ push: vi.fn() }),
}));

// 4) NextRequest constructor for API tests
class MockNextRequest {
  constructor(url: string, options: any = {}) {
    this.url = url;
    this.method = options.method || "GET";
    this.headers = new Headers(options.headers || {});
    this.body = options.body || null;
  }
  url: string;
  method: string;
  headers: Headers;
  body: any;
  nextUrl = { searchParams: new URLSearchParams() };
  json = vi.fn().mockResolvedValue({});
  text = vi.fn().mockResolvedValue("");
  formData = vi.fn().mockResolvedValue(new FormData());
}

// @ts-expect-error test shim
globalThis.NextRequest = MockNextRequest;

// 5) SWR must have a default export
vi.mock("swr", () => ({ default: vi.fn() }));

// 6) Client env module used by UI code (virtual mock is fine for tests)
vi.mock("@/lib/env/client", () => ({
  env: {
    NEXT_PUBLIC_SUPABASE_URL:
      process.env.NEXT_PUBLIC_SUPABASE_URL ??
      "https://btwaueeckvylrlrnbvgt.supabase.co",
    NEXT_PUBLIC_SUPABASE_ANON_KEY:
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ??
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ0d2F1ZWVja3Z5bHJscm5idmd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxNzMxNjIsImV4cCI6MjA2MDc0OTE2Mn0.06bTSo_WpKBjtXmgmMYKd0cYAOoi-xhydEjNZbo2GYk",
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    NEXT_PUBLIC_BACKEND_API_URL: process.env.NEXT_PUBLIC_BACKEND_API_URL,
    NEXT_PUBLIC_LAWS_API_BASE: process.env.NEXT_PUBLIC_LAWS_API_BASE,
    NEXT_PUBLIC_MCP_RULES_BASE_URL: process.env.NEXT_PUBLIC_MCP_RULES_BASE_URL,
    NEXT_PUBLIC_MCP_API_TIMEOUT: process.env.NEXT_PUBLIC_MCP_API_TIMEOUT,
    NEXT_PUBLIC_MCP_MAX_RETRIES: process.env.NEXT_PUBLIC_MCP_MAX_RETRIES,
    NEXT_PUBLIC_MCP_RETRY_DELAY: process.env.NEXT_PUBLIC_MCP_RETRY_DELAY,
    NEXT_PUBLIC_ENABLE_INSIGHTS: process.env.NEXT_PUBLIC_ENABLE_INSIGHTS,
    NEXT_PUBLIC_ENABLE_CHAT: process.env.NEXT_PUBLIC_ENABLE_CHAT,
    NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE:
      process.env.NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE,
    NEXT_PUBLIC_D2_STAGING_MODE: process.env.NEXT_PUBLIC_D2_STAGING_MODE,
    NEXT_PUBLIC_D3_PILOT_TENANT: process.env.NEXT_PUBLIC_D3_PILOT_TENANT,
    NEXT_PUBLIC_MCP_DEBUG_LOGGING: process.env.NEXT_PUBLIC_MCP_DEBUG_LOGGING,
    NEXT_PUBLIC_MCP_METRICS_ENABLED:
      process.env.NEXT_PUBLIC_MCP_METRICS_ENABLED,
    NEXT_PUBLIC_VERCEL_ENV: process.env.NEXT_PUBLIC_VERCEL_ENV,
    NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
    NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION,
    NEXT_PUBLIC_FPJS_API_KEY: process.env.NEXT_PUBLIC_FPJS_API_KEY,
    NEXT_PUBLIC_FPJS_PUBLIC_KEY: process.env.NEXT_PUBLIC_FPJS_PUBLIC_KEY,
    NEXT_PUBLIC_COPILOT_CLOUD_API_KEY:
      process.env.NEXT_PUBLIC_COPILOT_CLOUD_API_KEY,
    NEXT_PUBLIC_QUEUE_POLLING_INTERVAL:
      process.env.NEXT_PUBLIC_QUEUE_POLLING_INTERVAL,
    NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS:
      process.env.NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS,
    NEXT_PUBLIC_SUPER_ADMIN_EMAILS: process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAILS,
    NODE_ENV: process.env.NODE_ENV ?? "test",
  },
  clientEnv: {
    NEXT_PUBLIC_SUPABASE_URL:
      process.env.NEXT_PUBLIC_SUPABASE_URL ??
      "https://btwaueeckvylrlrnbvgt.supabase.co",
    NEXT_PUBLIC_SUPABASE_ANON_KEY:
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ??
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ0d2F1ZWVja3Z5bHJscm5idmd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxNzMxNjIsImV4cCI6MjA2MDc0OTE2Mn0.06bTSo_WpKBjtXmgmMYKd0cYAOoi-xhydEjNZbo2GYk",
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    NEXT_PUBLIC_BACKEND_API_URL: process.env.NEXT_PUBLIC_BACKEND_API_URL,
    NEXT_PUBLIC_LAWS_API_BASE: process.env.NEXT_PUBLIC_LAWS_API_BASE,
    NEXT_PUBLIC_MCP_RULES_BASE_URL: process.env.NEXT_PUBLIC_MCP_RULES_BASE_URL,
    NEXT_PUBLIC_MCP_API_TIMEOUT: process.env.NEXT_PUBLIC_MCP_API_TIMEOUT,
    NEXT_PUBLIC_MCP_MAX_RETRIES: process.env.NEXT_PUBLIC_MCP_MAX_RETRIES,
    NEXT_PUBLIC_MCP_RETRY_DELAY: process.env.NEXT_PUBLIC_MCP_RETRY_DELAY,
    NEXT_PUBLIC_ENABLE_INSIGHTS: process.env.NEXT_PUBLIC_ENABLE_INSIGHTS,
    NEXT_PUBLIC_ENABLE_CHAT: process.env.NEXT_PUBLIC_ENABLE_CHAT,
    NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE:
      process.env.NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE,
    NEXT_PUBLIC_D2_STAGING_MODE: process.env.NEXT_PUBLIC_D2_STAGING_MODE,
    NEXT_PUBLIC_D3_PILOT_TENANT: process.env.NEXT_PUBLIC_D3_PILOT_TENANT,
    NEXT_PUBLIC_MCP_DEBUG_LOGGING: process.env.NEXT_PUBLIC_MCP_DEBUG_LOGGING,
    NEXT_PUBLIC_MCP_METRICS_ENABLED:
      process.env.NEXT_PUBLIC_MCP_METRICS_ENABLED,
    NEXT_PUBLIC_VERCEL_ENV: process.env.NEXT_PUBLIC_VERCEL_ENV,
    NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
    NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION,
    NEXT_PUBLIC_FPJS_API_KEY: process.env.NEXT_PUBLIC_FPJS_API_KEY,
    NEXT_PUBLIC_FPJS_PUBLIC_KEY: process.env.NEXT_PUBLIC_FPJS_PUBLIC_KEY,
    NEXT_PUBLIC_COPILOT_CLOUD_API_KEY:
      process.env.NEXT_PUBLIC_COPILOT_CLOUD_API_KEY,
    NEXT_PUBLIC_QUEUE_POLLING_INTERVAL:
      process.env.NEXT_PUBLIC_QUEUE_POLLING_INTERVAL,
    NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS:
      process.env.NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS,
    NEXT_PUBLIC_SUPER_ADMIN_EMAILS: process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAILS,
    NODE_ENV: process.env.NODE_ENV ?? "test",
  },
  isDevelopment: false,
  isProduction: false,
  getSupabaseConfig: () => ({
    url:
      process.env.NEXT_PUBLIC_SUPABASE_URL ??
      "https://btwaueeckvylrlrnbvgt.supabase.co",
    anonKey:
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ??
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ0d2F1ZWVja3Z5bHJscm5idmd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUxNzMxNjIsImV4cCI6MjA2MDc0OTE2Mn0.06bTSo_WpKBjtXmgmMYKd0cYAOoi-xhydEjNZbo2GYk",
  }),
  getFingerprintJSConfig: () => ({
    publicKey: process.env.NEXT_PUBLIC_FPJS_PUBLIC_KEY,
  }),
  getCopilotKitConfig: () => ({
    apiKey: process.env.NEXT_PUBLIC_COPILOT_CLOUD_API_KEY,
  }),
}));

// 7) Default auth gate for API tests: allow unless a test overrides
vi.mock("@/lib/auth/payment-auth", () => ({
  authorizeRequest: vi.fn().mockResolvedValue({
    ok: true,
    userId: "user-123",
    roles: ["attorney"],
  }),
  withPaymentAuth: vi.fn((handler) => handler),
}));

// 8) Supabase client mocks for service tests
vi.mock("@/lib/supabase/server", () => ({
  createServerClient: vi.fn(() => ({
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue({ data: null, error: null }),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    rpc: vi.fn().mockResolvedValue({ data: null, error: null }),
    auth: {
      getSession: vi.fn().mockResolvedValue({ data: { session: null }, error: null }),
      getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null }),
    },
  })),
  createServerClientForUser: vi.fn(() => ({
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue({ data: null, error: null }),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    rpc: vi.fn().mockResolvedValue({ data: null, error: null }),
  })),
}));

// 9) Common React hooks and contexts
vi.mock("@/contexts/UserContext", () => ({
  useUser: vi.fn(() => ({
    user: {
      id: "user-123",
      email: "<EMAIL>",
      app_metadata: { is_super_admin: false },
    },
    loading: false,
  })),
  UserProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// 10) Timer utilities for tests that need fake timers
export const setupFakeTimers = () => {
  vi.useFakeTimers();
  vi.setSystemTime(new Date('2024-01-01T00:00:00Z'));
};

export const teardownFakeTimers = () => {
  vi.useRealTimers();
};

import { expect, afterEach, beforeEach } from "vitest";
import { cleanup } from "@testing-library/react";
import * as matchers from "@testing-library/jest-dom/matchers";

// Extend expect with jest-dom matchers
expect.extend(matchers);

// Cleanup after each test
afterEach(() => {
  cleanup();
});
