import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    // Integration test environment
    environment: 'jsdom',
    
    // Test file patterns - only integration tests
    include: [
      'src/**/*.integration.test.{ts,tsx}',
      'src/**/*.integration.spec.{ts,tsx}'
    ],
    
    // Exclude unit tests and other patterns
    exclude: [
      'node_modules/**',
      'dist/**',
      '.next/**',
      'src/**/*.test.{ts,tsx}',
      'src/**/*.spec.{ts,tsx}',
      'src/**/*.unit.test.{ts,tsx}',
      'src/**/*.e2e.test.{ts,tsx}'
    ],
    
    // Setup files for integration tests
    setupFiles: [
      './src/test/integration.setup.ts'
    ],
    
    // Global test configuration
    globals: true,
    
    // Test timeout for integration tests (longer than unit tests)
    testTimeout: 30000,
    
    // Hook timeouts
    hookTimeout: 10000,
    
    // Retry failed tests once (integration tests can be flaky)
    retry: 1,
    
    // Run tests in sequence to avoid conflicts
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true
      }
    },
    
    // Coverage configuration for integration tests
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage/integration',
      include: [
        'src/**/*.{ts,tsx}'
      ],
      exclude: [
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/**/*.stories.{ts,tsx}',
        'src/test/**',
        'src/**/__tests__/**',
        'src/**/__mocks__/**'
      ],
      thresholds: {
        // Lower thresholds for integration tests
        global: {
          branches: 50,
          functions: 50,
          lines: 50,
          statements: 50
        }
      }
    },
    
    // Reporter configuration
    reporter: [
      'default',
      'json',
      'html'
    ],
    
    // Output directory for test results
    outputFile: {
      json: './test-results/integration-results.json',
      html: './test-results/integration-report.html'
    },
    
    // Mock configuration
    deps: {
      // External dependencies to mock
      external: [
        // Don't mock these in integration tests
      ],
      // Inline dependencies for better integration testing
      inline: [
        // Mock these inline
        '@supabase/supabase-js',
        'openai'
      ]
    },
    
    // Environment variables for integration tests
    env: {
      NODE_ENV: 'test',
      VITEST_INTEGRATION: 'true',
      // Test-specific environment variables
      NEXT_PUBLIC_SUPABASE_URL: 'http://localhost:54321',
      NEXT_PUBLIC_SUPABASE_ANON_KEY: 'test-anon-key',
      SUPABASE_SERVICE_ROLE_KEY: 'test-service-role-key',
      OPENAI_API_KEY: 'test-openai-key',
      // Disable external services in integration tests
      DISABLE_EXTERNAL_APIS: 'true'
    },
    
    // Watch configuration
    watch: false, // Disabled by default for CI
    
    // Bail on first failure in CI
    bail: process.env.CI ? 1 : 0,
    
    // Silent mode in CI
    silent: process.env.CI === 'true',
    
    // Test name pattern for filtering
    testNamePattern: process.env.VITEST_TEST_NAME_PATTERN,
    
    // Workspace configuration
    workspace: [
      {
        test: {
          name: 'integration',
          root: './src',
          include: ['**/*.integration.test.{ts,tsx}']
        }
      }
    ]
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/test': path.resolve(__dirname, './src/test'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/lib': path.resolve(__dirname, './src/lib'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/config': path.resolve(__dirname, './src/config')
    }
  },
  
  // Define configuration
  define: {
    'process.env.NODE_ENV': '"test"',
    'process.env.VITEST_INTEGRATION': '"true"'
  },
  
  // Esbuild configuration
  esbuild: {
    target: 'node14'
  },
  
  // Optimizations
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@testing-library/react',
      '@testing-library/jest-dom',
      'msw'
    ]
  }
});
