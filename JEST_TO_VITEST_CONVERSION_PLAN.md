# Jest → Vitest Test Conversion Plan

## Legacy Test Inventory (28 files total)

### 🟢 EASY - Quick Wins (Convert First)
**Pure functions, small components, simple hooks with minimal dependencies**

1. **permissions.test.ts** (317 lines) - Already using Vitest, pure auth logic functions
2. **ProactiveContainer.test.tsx** (125 lines) - React component with basic mocks
3. **payment-method-validation-service.test.ts** (502 lines) - Service validation logic
4. **subscription-service.test.ts** - Service layer tests
5. **usage-tracking-service.test.ts** - Service layer tests

**Conversion effort:** ~1-2 hours each
**Patterns needed:** `jest.fn` → `vi.fn`, `jest.spyOn` → `vi.spyOn`, basic React testing

---

### 🟡 MEDIUM - Moderate Complexity
**Auth helpers, complex mocks, timer-based tests**

6. **auth-hooks.test.tsx** - Hook testing with auth context
7. **super-admin-config.test.ts** - Configuration testing
8. **ag-ui-auth.test.ts** - UI auth component testing
9. **jwt-utils.test.ts** - JWT utility functions
10. **insights-api.test.ts** - API route testing
11. **insights-route.test.ts** - Route handler testing
12. **voice-metrics-tests/page.test.tsx** - Page component testing
13. **voice-metrics-tests/rbac.test.tsx** - RBAC component testing

**Conversion effort:** ~2-4 hours each
**Patterns needed:** `vi.spyOn` on modules, timer mocks, API route testing patterns

---

### 🔴 HARD - Complex/Integration (Keep in Legacy for Now)
**Integration tests, E2E tests, heavy module rewiring, external dependencies**

14. **validate-jwt.jwks.test.ts** - JWKS validation (server-only imports)
15. **payment-auth.test.ts** - Complex payment auth integration
16. **with-payment-auth.int.test.ts** - Integration test
17. **documents-api-tests/analyze-route.test.ts** - Document analysis API
18. **documents-api-tests/upload-route.test.ts** - File upload API
19. **subscription-integration.test.ts** - Subscription integration
20. **subscription-usage-integration.test.ts** - Usage integration
21. **payment-method-validation-service.test.ts** - Complex validation service
22. **voice-metrics-tests/integration.test.tsx** - Integration test
23. **usage-tracking-service.unit.test.ts** - Complex service with external deps

**E2E Tests (Keep in Legacy):**
24. **e2e/payment-flows/3d-secure.test.ts**
25. **e2e/payment-flows/checkout-minimal.test.ts**
26. **e2e/payment-flows/checkout.test.ts**
27. **e2e/payment-flows/failure-recovery.test.ts**
28. **e2e/payment-flows/multi-currency.test.ts**
29. **e2e/payment-flows/subscription.test.ts**

**Conversion effort:** ~8+ hours each, requires infrastructure work
**Reasons to defer:** External APIs, complex mocking, integration dependencies

---

## Conversion Strategy

### Phase 1: Infrastructure Enhancement
- Enhance `vitest.setup.ts` with centralized mocks
- Add WebCrypto shim for JWKS tests
- Create virtual `@/lib/env/client` mock
- Setup default `authorizeRequest` mock with per-test overrides

### Phase 2: Easy Wins (Target: 5 tests)
1. **permissions.test.ts** - Already Vitest, just needs import fixes
2. **ProactiveContainer.test.tsx** - Simple React component
3. **payment-method-validation-service.test.ts** - Pure validation logic
4. **subscription-service.test.ts** - Service layer
5. **usage-tracking-service.test.ts** - Service layer

### Phase 3: Medium Complexity (Target: 5 tests)
6. **auth-hooks.test.tsx** - Hook testing patterns
7. **super-admin-config.test.ts** - Config testing
8. **ag-ui-auth.test.ts** - UI component
9. **jwt-utils.test.ts** - Utility functions
10. **insights-api.test.ts** - API testing

### Phase 4: Documentation & Tracking
- Update GitHub tracking issue
- Document conversion patterns
- Report metrics and next steps

---

## Jest → Vitest Conversion Cheat Sheet

| Jest | Vitest |
|------|--------|
| `jest.fn()` | `vi.fn()` |
| `jest.spyOn(obj, 'method')` | `vi.spyOn(obj, 'method')` |
| `jest.mock('module', factory)` | `vi.mock('module', factory)` |
| `jest.requireActual('module')` | `await vi.importActual<any>('module')` |
| `jest.useFakeTimers()` | `vi.useFakeTimers()` |
| `jest.resetAllMocks()` | `vi.resetAllMocks()` |

**Key Differences:**
- Mock factories must return objects; for default exports: `{ default: ... }`
- Use `vi.hoisted()` for variables used in mock factories
- Prefer `vi.spyOn` over deep module rewiring
- Use async mock factories for `vi.importActual`

---

## Success Metrics

**Current State:** 18 test files, 209 tests passing
**Target:** +10 test files, +50-100 tests passing
**Timeline:** 2-3 weeks for easy/medium tests

**Quality Gates:**
- Zero TypeScript errors
- Zero ESLint warnings  
- All converted tests pass in CI
- No regressions in existing tests
